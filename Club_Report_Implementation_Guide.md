# Club Report System - Complete Implementation Guide

## Overview
The Club Report system generates daily operational reports for individual fitness clubs, providing critical business intelligence on guest activities, member acquisitions, payment issues, and operational exceptions. This document provides everything needed to implement this system in another environment.

## System Architecture

### Core Components
1. **Report Library**: `Club_report.php` - Main report generation class
2. **Data Model**: `Report_model.php` - Data aggregation and PDF generation
3. **Dashboard Model**: `Dashboard_model.php` - Database queries for report data
4. **Email Service**: `Emails.php` - Automated report distribution
5. **PDF Template**: `club_report.php` - Report formatting and styling
6. **Configuration**: `ut_reports.php` - Report structure and headers

### Report Sections
The club report includes the following sections:
1. **First Time Missed Guest** - Guests who checked in but weren't properly processed
2. **Members in Queue** - Members with pending agreement processing
3. **Red Alert Check-ins** - Members checking in with account issues
4. **Daily New Members** - New member acquisitions for the day
5. **Daily Rewrites** - Agreement modifications and updates
6. **Effective Cancels** - Member terminations taking effect
7. **Cash App Transactions** - Alternative payment method tracking
8. **Processing Fees on ACH** - EFT payment processing fees
9. **No Processing Fees on CC** - Credit card payments without fees
10. **Downgrade No Fees** - Membership downgrades without enrollment fees
11. **Draft Changes** - Payment method changes from bank draft to credit card
12. **Delinquent Prior** - Members with past due balances

## Implementation Components

### 1. Report Library Class
**File**: `application/libraries/reports/Club_report.php`

```php
<?php
defined('BASEPATH') OR exit('No direct script access allowed');
include_once 'Report_lib.php';

class Club_report extends Report_lib
{
    public function send_club_report(int $club_id, ?bool $cli_output = FALSE)
    {
        try {
            $this->CI->load->model('admin/report_model');
            $message = '';
            $report = NULL;

            try {
                $report = $this->CI->report_model->club_report($club_id);
            } catch (Exception $e) {
                $message = ': '.$e->getMessage();
            }

            $club_name = $this->_get_club_name($club_id);

            return $this->send_report(
                NULL,
                [
                    'report_type' => 'club_report',
                    'subject' => $club_name ? 'Crunch '.$club_name.': Daily Activity Report' : $club_name,
                    'body' => 'Daily Activity Report'.$message,
                    'report' => $report,
                    'param' => ['club_id' => $club_id],
                ],
                $cli_output
            );
        } catch (Exception $e) {
            log_message('error', print_r($e->getMessage(), TRUE));
            return $e->getMessage();
        }
    }
}
```

### 2. Report Model Method
**File**: `application/models/admin/Report_model.php`

```php
public function club_report(int $club_id)
{
    $club_name = $this->dashboard_model->get_club_name_by_id($club_id);
    $yesterday = date('Y-m-d', strtotime('yesterday'));
    
    $filename = 'daily_club_report-'.$club_name;
    $filename .= '_'.date('Ymd_His');
    $filename .= '.pdf';

    $data_tables = [
        'guest_check_in' => $this->dashboard_model->get_guest_check_in($club_id, $yesterday),
        'members_in_queue' => $this->dashboard_model->get_members_in_queue($club_id),
        'red_alert_checkin' => $this->dashboard_model->get_red_alert_checkin($club_id, $yesterday),
        'daily_new_members' => $this->dashboard_model->get_daily_new_members($club_id, $yesterday),
        'daily_rewrites' => $this->dashboard_model->get_daily_rewrites($club_id, $yesterday),
        'effective_cancels' => $this->dashboard_model->get_effective_cancels($club_id),
        'cashapp' => $this->dashboard_model->get_cashapp($club_id, $yesterday),
        'profees_on_ACH' => $this->dashboard_model->get_profees_on_ACH($club_id, $yesterday),
        'noprofees_on_cc' => $this->dashboard_model->get_noprofees_on_cc($club_id, $yesterday),
        'downgrade_no_fees' => $this->dashboard_model->get_downgrade_no_fees($club_id, $yesterday),
        'draft_changes' => $this->dashboard_model->get_draft_changes($club_id),
        'delinquent_prior' => $this->dashboard_model->get_delinquent_prior($club_id),
    ];

    return $this->_generate_report(
        $filename,
        [
            'title' => $club_name.' Daily Activity Report for '.$yesterday,
            'format' => 'A4-L',
            'template' => 'pdf/club_report',
        ],
        [
            'tables' => get_ut_reports()['club_report']['tables'] ?? NULL,
            'data' => $data_tables,
        ],
        'pdf'
    );
}
```

### 3. Email Service Integration
**File**: `application/controllers/services/Emails.php`

```php
public function generate_clubs_report()
{
    $this->_run_for_all_clubs([$this, '_send_daily_club_report']);
}

private function _send_daily_club_report(int $club_id): void
{
    $this->load->library('reports/Club_report', NULL, 'Club_report');
    $this->_write_to_logs(
        $this->Club_report->send_club_report($club_id, TRUE),
        'daily_club_report_'.$club_id,
        'daily_emails'
    );
}
```

### 4. Shell Script Integration
**File**: `CRONs/emails/daily_club_report.sh`

```bash
#!/bin/sh
cd "$(dirname "$0")"
. ../env_settings/env_settings
cd "$LOCAL_PATH"; php index.php services/Emails generate_clubs_report > /dev/null 2>&1
```

## Database Queries and Data Models

### Dashboard Model Methods
**File**: `application/models/admin/Dashboard_model.php`

#### 1. Guest Check-in Data
```php
public function get_guest_check_in($club, $yesterday)
{
    $query = "
        SELECT *
        FROM(
         SELECT m.*, MAX(c.checkInTimeStamp) AS PriorCheckIn
         FROM(
                        SELECT  a.member_id,
        				CONCAT(p.firstname, ' ', p.lastName) AS Guest, p.primaryphone, p.mobilePhone,p.email,
                        a.campaignName,
                        a.referringMemberName,
                        IF(member_misc_2 is null, agreementEntrySource, member_misc_2) AS OriginalSignUpSource,
                        memberMisc1, firstCheckInTimestamp,
                        Time_format(lastCheckInTimestamp, '%r') AS CheckInTime, lastCheckInTimestamp
                        FROM abc_checkins ci USE INDEX (checkInTimeStamp,checkin_club_id_local)
                        LEFT JOIN abc_clubs c ON c.id=ci.checkin_club_id_local
                        LEFT JOIN abc_members m on m.id=ci.member_id_local
                        LEFT JOIN abc_members_agreements a ON ci.member_id_local=a.member_id
                        LEFT JOIN abc_members_personaldata p ON p.member_id=a.member_id
                        LEFT JOIN abc_ftp_member_misc mm ON mm.member_id=m.memberId
                        LEFT JOIN abc_member_statuses ms ON ms.id=p.memberStatus_id
                        LEFT JOIN udt_prospect_duplicates pd ON pd.member_id=a.member_id
                        LEFT JOIN udt_agreements ua ON pd.primary_member_id=ua.member_id
                        WHERE checkInTimeStamp>='{$yesterday} 00:00:00' AND checkInTimeStamp<='{$yesterday} 23:59:59'
                        AND ci.checkin_club_id_local='{$club}'
                        AND ((a.agreementnumber='PROSP' AND
                        DATE(p.firstCheckInTimestamp) = '{$yesterday}')
                        OR (active=0 AND frozen=0 AND ms.name<>'Pending Cancel'))
                        AND (ua.Status is null OR ua.Status<>'Active')
                        GROUP BY a.member_id
                        ) AS m
         LEFT JOIN abc_checkins c ON c.member_id_local=m.member_id AND c.checkInTimeStamp<'{$yesterday} 00:00:00'
         GROUP BY m.member_id
         ) AS t
        ORDER BY Guest";

    return $this->db->query($query)->result_array();
}
```

#### 2. Members in Queue
```php
public function get_members_in_queue(int $club_id): array
{
    $this->db->select([
        'CONCAT(TRIM(p.firstname), " ", TRIM(p.lastname)) AS client',
        'a.agreementnumber',
        'a.currentQueue',
        'pp.name',
        'a.salespersonname',
        'a.signdate',
    ]);
    $this->db->from('abc_members_agreements a');
    $this->db->join('abc_clubs c', 'c.id = a.club_id', 'LEFT');
    $this->db->join('abc_members m', 'm.id=a.member_id', 'LEFT');
    $this->db->join('abc_members_personaldata p', 'a.member_id=p.member_id', 'LEFT');
    $this->db->join('abc_member_statuses ms', 'ms.id=p.memberStatus_id', 'LEFT');
    $this->db->join('abc_payment_plans pp', 'pp.id=a.paymentPlan_id', 'LEFT');
    $this->db->where('c.id', $club_id);
    $this->db->where_not_in('a.agreementNumber', ['Prospect']);
    $this->db->where('ms.active', '1');
    $this->db->where_not_in('a.currentQueue', ['Posted', 'Deleted']);
    $this->db->where('a.currentQueue IS NOT NULL');
    $this->db->order_by('a.signdate', 'DESC');

    return $this->db->get()->result_array();
}
```

#### 3. Red Alert Check-ins
```php
public function get_red_alert_checkin($club, $yesterday)
{
    $query = "
        SELECT CONCAT(p.firstName, ' ', p.lastname) AS Member,
        a.agreementnumber,
        checkInTimeStamp AS Checkin,
		a.membershipType,  s.id, s.name as MemberStatus,
        IF(begin_date>DATE(checkInTimeStamp), CONCAT('Checking In Before ', begin_date),p.memberStatusReason) AS memberStatusReason,
        CONCAT('$',ROUND(pastDueBalance,2)) AS pastDueBalance,
        p.memberStatusDate, nextBillingDate
    		FROM abc_checkins ci
    		INNER JOIN abc_clubs c ON c.id=checkin_club_id_local
            INNER JOIN abc_members_agreements a ON a.member_id=ci.member_id_local
            LEFT JOIN abc_members_personaldata p ON p.member_id=a.member_id
    		LEFT JOIN abc_member_statuses s ON s.id=p.memberStatus_id
            LEFT JOIN abc_ftp_active_inactive_members m ON m.member_id_local = ci.member_id_local
    		WHERE checkInTimeStamp >= '{$yesterday} 00:00:00' AND checkInTimeStamp <='{$yesterday} 23:59:59'
            AND c.id='{$club}'
            AND ((s.active=0 AND a.membershipType<>'Prospect' and s.id<>'1006') OR pastDueBalance>0  OR begin_date>DATE(checkInTimeStamp))
            AND (p.memberStatusDate<>Date(checkInTimeStamp) OR p.memberStatusDate is null)
            GROUP BY agreementnumber
            ORDER BY checkInTimeStamp";

    return $this->db->query($query)->result_array();
}
```

#### 4. Daily New Members
```php
public function get_daily_new_members($club, $yesterday)
{
    $query = "
        SELECT CONCAT(p.firstName, ' ' ,p.lastname) AS Member,
             a.agreementnumber,
             salesPersonName,
             membershipType, pp.name AS PaymentPlan, nextBillingDate, a.agreementPaymentMethod,
             IF(agreementEntrySource='Web' AND salespersonname is null, 'Web',
               IF(agreementEntrySource='Web' AND salespersonname is not null, 'VFP', agreementEntrySource)) AS JoinedFromSource,
               ea.eventNameRemote, ea.eventTimestampRemote
            FROM abc_members_agreements a
            LEFT JOIN abc_members m ON m.id=a.member_id
            LEFT JOIN abc_members_personaldata p  ON a.member_id=p.member_id
            LEFT JOIN abc_member_statuses ms ON ms.id=p.memberStatus_id
            LEFT JOIN abc_clubs c ON c.id=a.club_id
            LEFT JOIN abc_payment_plans pp ON pp.id=a.paymentPlan_id
            LEFT JOIN abc_event_attendances ea ON ea.member_id_local=m.id
            WHERE a.membershipType<>'Prospect' AND (a.currentQueue IN ('Posted', 'Approval') OR a.currentQueue is null)
            AND (pp.name is null OR (pp.EFT=1 AND pp.Employee=0))
            AND IF(lastRewriteDate is null OR sinceDate>='2030-01-01', signDate, sinceDate) ='{$yesterday}'
            AND a.club_id='{$club}'
            AND (attendedStatus is null OR attendedStatus NOT IN ('Did Not Attend'))
            GROUP BY a.id
            ORDER BY p.firstName, p.LastName";

    return $this->db->query($query)->result_array();
}
```

#### 5. Daily Rewrites
```php
public function get_daily_rewrites($club, $yesterday)
{
    $query = "SELECT CONCAT(p.firstName, ' ', p.lastName) AS Member,
                                a.agreementnumber,
            					pp.name AS PaymentPlan,
                                 a.salesPersonName,
                                 o.paymentPlan as OriginalPaymentPlan, o.salesPersonName as OriginalSalesPerson,
                                 ac.ChangeType, ac.DateChanged
            		FROM abc_members_agreements a
                    LEFT JOIN abc_members m ON m.id = a.member_id
                    LEFT JOIN abc_members_personaldata p ON a.member_id = p.member_id
                    LEFT JOIN abc_member_statuses ms ON ms.id = p.memberStatus_id
                    LEFT JOIN abc_payment_plans pp ON pp.id = a.paymentPlan_id
                    LEFT JOIN udt_agreements o ON o.agreement_id=a.id
                    LEFT JOIN (SELECT MAX(id) as MaxChange, agreement_id FROM udt_agreement_changes WHERE ChangeType NOT IN ('Agreement Change','Pending Cancel') GROUP BY agreement_id) AS ma ON ma.agreement_id=a.id
                    LEFT JOIN udt_agreement_changes ac ON ac.agreement_id=a.id AND ac.id=MaxChange
                    WHERE  a.lastrewritedate='{$yesterday}'
                    AND a.club_id='{$club}'
                    AND pp.upgraded=0 AND pp.Downgraded=0
                    AND pp.EFT=1
                    ORDER BY p.lastname, p.firstname;";

    return $this->db->query($query)->result_array();
}
```

#### 6. Effective Cancels
```php
public function get_effective_cancels(int $club_id, string $totime = 'yesterday', ?string $fromtime = NULL): array
{
    $this->db->select([
        "CONCAT(p.firstName, ' ' , p.lastName) AS Member",
        'a.agreementnumber',
        'p.email',
        'p.primaryPhone',
        'p.memberStatusReason',
        'a.signDate',
        'p.memberStatusDate as cancelEffective',
        'pp.name AS PaymentPlan',
        'p.firstCheckInTimestamp',
        'p.lastCheckInTimestamp',
        'p.totalCheckInCount',
    ]);
    $this->db->from('abc_members_agreements a');
    $this->db->join('abc_members m', 'm.id = a.member_id', 'left');
    $this->db->join('abc_members_personaldata p', 'a.member_id = p.member_id', 'left');
    $this->db->join('abc_member_statuses ms', 'ms.id = p.memberStatus_id', 'left');
    $this->db->join('abc_payment_plans pp', 'pp.id = a.paymentPlan_id', 'left');
    $this->db->join('abc_ftp_pending_cancels pc', 'pc.member_id_local = a.member_id', 'left');
    $this->db->where('a.membershipType <>', 'Prospect');
    $this->db->where_in('a.currentQueue', ['Posted', 'Approval']);
    $this->db->where('ms.cancel', 1);
    $this->db->where('pp.Employee = 0');
    $this->db->where('a.club_id', $club_id);

    if (empty($fromtime)) {
        $this->db->where('pc.pending_cancel_date', date('Y-m-d', strtotime($totime)));
    } else {
        $this->db->where('pc.pending_cancel_date >=', date('Y-m-d', strtotime($fromtime)));
        $this->db->where('pc.pending_cancel_date <=', date('Y-m-d', strtotime($totime)));
    }

    $this->db->order_by('p.lastName, p.firstName');
    return $this->db->get()->result_array();
}
```

#### 7. Cash App Transactions
```php
public function get_cashapp($club, $yesterday)
{
    $query = "SELECT b.member_name_last_first AS `member`, b.agreement_number AS agreementnumber,
                salesPersonName, agreement_payment_plan,
                agr_bank_routing_number,
                a.signDate, b.next_billing_date,
                IF(alt_cc_on_file=1,'Yes','No') AS AltCCOnFile,
                CONCAT('$',FORMAT(b.next_due_amount,2)) AS next_due_amount
                FROM abc_ftp_alternate_billing b
                LEFT JOIN abc_members_agreements a ON a.member_id=b.member_id_local
                LEFT JOIN abc_members_personaldata p ON p.member_id=a.member_id
                LEFT JOIN abc_member_statuses ms ON ms.id=p.memberStatus_id
                WHERE b.club_id_local = '{$club}'
                AND agr_bank_routing_number IN ('*********','*********','*********')
                AND member_active_status=1 and Active=1
                AND signDate=DATE_ADD(CURDATE(), INTERVAL -1 DAY)
                LIMIT 500";

    return $this->db->query($query)->result_array();
}
```

#### 8. Processing Fees on ACH
```php
public function get_profees_on_ACH($club, $yesterday)
{
    $query = "SELECT CONCAT(p.firstname, ' ', p.lastname) AS member,
            	a.agreementnumber,
                salesPersonName,
                pp.name AS PaymentPlan,
                signDate,
                invoice_date,
                CONCAT('$', ROUND(i.invoice_amount,2)) AS invoice_amt,
                agreementPaymentMethod
            FROM abc_members_agreements a
            INNER JOIN abc_ftp_invoices_future_procfees i ON a.agreementNumber=i.member AND a.club_id=i.club_id_local
	        LEFT JOIN abc_members_personaldata p  ON a.member_id=p.member_id
            LEFT JOIN abc_member_statuses ms ON ms.id = p.memberStatus_id
            LEFT JOIN abc_payment_plans pp ON pp.id = a.paymentPlan_id
            LEFT JOIN abc_club_settings cs ON cs.club_id=a.club_id
            WHERE a.club_id='{$club}'
            AND a.membershipType<>'Prospect' AND (a.currentQueue IN ('Posted', 'Approval') OR a.currentQueue is null)
            AND (pp.name is null OR (pp.EFT=1 AND pp.Employee=0))
             AND agreementPaymentMethod='EFT'
            AND ms.active=1
            AND GrandOpening<CURDATE()
            LIMIT 500";

    return $this->db->query($query)->result_array();
}
```

#### 9. No Processing Fees on Credit Card
```php
public function get_noprofees_on_cc($club, $yesterday)
{
    $query = "
        SELECT CONCAT(p.firstName, ' ' ,p.lastname) AS Member,
             a.agreementnumber,
             salesPersonName,
             pp.name AS PaymentPlan,
             a.signDate,
             nextBillingDate,
             CONCAT('$', ROUND(nextDueAmount,2)) AS nextDues,
             a.agreementPaymentMethod
            FROM abc_members_agreements a
            LEFT JOIN abc_members_personaldata p  ON a.member_id=p.member_id
            LEFT JOIN abc_member_statuses ms ON ms.id=p.memberStatus_id
            LEFT JOIN abc_club_settings cs ON cs.club_id=a.club_id
            LEFT JOIN abc_payment_plans pp ON pp.id=a.paymentPlan_id
            LEFT JOIN abc_ftp_invoices_future_procfees i ON a.agreementNumber=i.member AND a.club_id=i.club_id_local
            WHERE a.club_id='{$club}'
            AND a.membershipType<>'Prospect' AND (a.currentQueue IN ('Posted', 'Approval') OR a.currentQueue is null)
            AND (pp.name is null OR (pp.EFT=1 AND pp.Employee=0))
            AND agreementPaymentMethod='Credit Card'
            AND i.id is null
            AND ms.active=1
            AND GrandOpening<CURDATE()
            AND pp.id NOT IN ('999','5525')
            AND nextBillingDate<=DATE_ADD(CURDATE(), INTERVAL 29 DAY)
            GROUP BY a.id
            LIMIT 500";

    return $this->db->query($query)->result_array();
}
```

#### 10. Downgrade No Fees
```php
public function get_downgrade_no_fees($club, $yesterday)
{
    $query = "
        SELECT c.club_id, ac.name AS Club, a.member_id, c.salesPersonName AS SalesPersonOnLastTransaction,
            a.agreementNumber, CONCAT(a.firstname, ' ', a.lastname) AS Member, a.Date AS OrginalPlanDate, a.PaymentPlan AS OriginalPaymentPlan,
            ChangeType,  DateChanged, c.paymentPlan, c.DateCaptured,
            datediff(Datechanged,a.date) AS DaysToDowngrade,
            CONCAT('$',FORMAT(t.unitPrice,2)) AS EnrollmentFee
            FROM udt_agreement_changes c
            LEFT JOIN udt_agreements a ON a.agreement_id=c.agreement_id
            LEFT JOIN abc_clubs ac ON ac.id=c.club_id
            LEFT JOIN (SELECT member_id_local, t.receiptnumber, DATE(t.transactionTimestamp) AS TransDate, t.stationName, i.name, i.inventoryType, i.upc,i.unitPrice
            FROM abc_pos_transactions t
            LEFT JOIN abc_pos_transaction_items i ON i.pos_transaction_id=t.id
            WHERE transactionTimestamp>='{$yesterday} 00:00:00' AND transactionTimestamp<='{$yesterday} 23:59:59'
            AND profitCenter='Enrollment Fees') AS t ON t.member_id_local=a.member_id AND t.TransDate=DateChanged
            WHERE ChangeType IN ('Downgraded','Agreement Change')
            AND c.paymentPlanId<>a.paymentPlanId
            AND c.paymentplan<>'Employee'
            AND DateChanged>='{$yesterday}' AND DateChanged<='{$yesterday}'
            AND a.PaymentPlan NOT like '%%Base%%' AND c.paymentPlan NOT LIKE '%%Peak%%'
            AND (t.unitPrice<40 OR t.unitPrice is null)
            AND a.club_id='{$club}'";

    return $this->db->query($query)->result_array();
}
```

#### 11. Draft Changes
```php
public function get_draft_changes($club)
{
    $query = "SELECT c.club_name, c.agreement_number,
                    CONCAT(c.first_name, ' ', c.last_name) AS Member,
                    c.membership_type_description,
                    c.home_phone, c.cell_phone, c.email_address, p.mode, c.mode as CC
                    FROM abc_ftp_active_inactive_members_dayprior p
                    LEFT JOIN abc_ftp_active_inactive_members c ON c.agreement_number=p.agreement_number
                    LEFT JOIN abc_clubs cl ON p.clubNumberRemote=cl.clubNumber
                    WHERE p.mode='Bank Draft' AND c.mode IN ('Amex','Discover','MasterCard','Visa')
                    AND cl.id='{$club}';";

    return $this->db->query($query)->result_array();
}
```

#### 12. Delinquent Prior
```php
public function get_delinquent_prior($club)
{
    $query = "SELECT agreement_number,
                        CONCAT(first_name, ' ' , last_name) AS Member,
                        home_phone, cell_phone, email_address,
                        membership_type_description, mode as payment_method,
                        next_due_date, CONCAT('$',past_due_1_30) AS past_due
                        FROM abc_ftp_active_inactive_members
                        WHERE past_due_1_30<>0 AND next_due_date>=DATE_ADD(CURDATE(), INTERVAL -2 DAY)
                        AND club_id_local='{$club}'
                        ORDER BY club_id_local, past_due_1_30 DESC";

    return $this->db->query($query)->result_array();
}
```

## Report Configuration

### Report Structure Configuration
**File**: `application/config/ut_reports.php`

```php
'club_report' => [
    'report_title' => 'Daily Club Report',
    'data_load_page' => base_url('admin/report/club_report'),
    'by_club' => TRUE,
    'by_date_from' => FALSE,
    'by_date_to' => FALSE,
    'tables' => [
        'guest_check_in' => [
            'title' => 'First Time Missed Guest',
            'headers' => [
                'Guest' => ['name' => 'Guest'],
                'primaryphone' => ['name' => 'Primary Phone'],
                'mobilePhone' => ['name' => 'Mobile Phone'],
                'email' => ['name' => 'Email'],
                'campaignName' => ['name' => 'How Heard'],
                'referringMemberName' => ['name' => 'Referring Member'],
                'OriginalSignUpSource' => ['name' => 'Original Source'],
                'memberMisc1' => ['name' => 'Pass/Peaks Guest'],
                'CheckInTime' => ['name' => 'Check-In Time'],
            ],
            'total' => 'Count',
        ],
        'members_in_queue' => [
            'title' => 'Members in Queue',
            'headers' => [
                'client' => ['name' => 'Member'],
                'agreementnumber' => ['name' => 'Agreement'],
                'currentQueue' => ['name' => 'Queue Status'],
                'name' => ['name' => 'Payment Plan'],
                'salespersonname' => ['name' => 'Sales Person'],
                'signdate' => ['name' => 'Sign Date'],
            ],
            'total' => 'Count',
        ],
        'red_alert_checkin' => [
            'title' => 'Red Alert Check-ins',
            'headers' => [
                'Member' => ['name' => 'Member'],
                'agreementnumber' => ['name' => 'Agreement'],
                'Checkin' => ['name' => 'Check-in Time'],
                'membershipType' => ['name' => 'Membership Type'],
                'MemberStatus' => ['name' => 'Status'],
                'memberStatusReason' => ['name' => 'Status Reason'],
                'pastDueBalance' => ['name' => 'Past Due'],
            ],
            'total' => 'Count',
        ],
        'daily_new_members' => [
            'title' => 'Daily New Members',
            'headers' => [
                'Member' => ['name' => 'Member'],
                'agreementnumber' => ['name' => 'Agreement'],
                'salesPersonName' => ['name' => 'Sales Person'],
                'membershipType' => ['name' => 'Membership Type'],
                'PaymentPlan' => ['name' => 'Payment Plan'],
                'nextBillingDate' => ['name' => 'Next Billing'],
                'agreementPaymentMethod' => ['name' => 'Payment Method'],
                'JoinedFromSource' => ['name' => 'Source'],
            ],
            'total' => 'Count',
        ],
        'daily_rewrites' => [
            'title' => 'Daily Rewrites',
            'headers' => [
                'Member' => ['name' => 'Member'],
                'agreementnumber' => ['name' => 'Agreement'],
                'PaymentPlan' => ['name' => 'Current Plan'],
                'salesPersonName' => ['name' => 'Sales Person'],
                'OriginalPaymentPlan' => ['name' => 'Original Plan'],
                'OriginalSalesPerson' => ['name' => 'Original Sales Person'],
                'ChangeType' => ['name' => 'Change Type'],
                'DateChanged' => ['name' => 'Date Changed'],
            ],
            'total' => 'Count',
        ],
        'effective_cancels' => [
            'title' => 'Effective Cancels',
            'headers' => [
                'Member' => ['name' => 'Member'],
                'agreementnumber' => ['name' => 'Agreement'],
                'email' => ['name' => 'Email'],
                'primaryPhone' => ['name' => 'Phone'],
                'memberStatusReason' => ['name' => 'Cancel Reason'],
                'signDate' => ['name' => 'Sign Date'],
                'cancelEffective' => ['name' => 'Cancel Date'],
                'PaymentPlan' => ['name' => 'Payment Plan'],
                'totalCheckInCount' => ['name' => 'Total Visits'],
            ],
            'total' => 'Count',
        ],
    ],
],
```

## PDF Template

### Report Template
**File**: `application/views/pdf/club_report.php`

```php
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; font-size: 10px; }
        .header { text-align: center; margin-bottom: 20px; }
        .table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        .table th, .table td { border: 1px solid #ddd; padding: 5px; text-align: left; }
        .table th { background-color: #f2f2f2; font-weight: bold; }
        .section-title { font-size: 12px; font-weight: bold; margin: 15px 0 5px 0; }
        .total-row { font-weight: bold; background-color: #f9f9f9; }
        .no-data { text-align: center; font-style: italic; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1><?= $title ?></h1>
        <p>Generated on: <?= date('Y-m-d H:i:s') ?></p>
    </div>

    <?php if (!empty($tables) && !empty($data)): ?>
        <?php foreach ($tables as $table_key => $table_config): ?>
            <?php if (isset($data[$table_key])): ?>
                <div class="section-title"><?= $table_config['title'] ?></div>

                <?php if (!empty($data[$table_key])): ?>
                    <table class="table">
                        <thead>
                            <tr>
                                <?php foreach ($table_config['headers'] as $header_key => $header_config): ?>
                                    <th><?= $header_config['name'] ?></th>
                                <?php endforeach; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($data[$table_key] as $row): ?>
                                <tr>
                                    <?php foreach ($table_config['headers'] as $header_key => $header_config): ?>
                                        <td><?= isset($row[$header_key]) ? htmlspecialchars($row[$header_key]) : '' ?></td>
                                    <?php endforeach; ?>
                                </tr>
                            <?php endforeach; ?>

                            <?php if (isset($table_config['total'])): ?>
                                <tr class="total-row">
                                    <td colspan="<?= count($table_config['headers']) - 1 ?>">
                                        <strong><?= $table_config['total'] ?>:</strong>
                                    </td>
                                    <td><strong><?= count($data[$table_key]) ?></strong></td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <div class="no-data">No data available for this section</div>
                <?php endif; ?>
            <?php endif; ?>
        <?php endforeach; ?>
    <?php else: ?>
        <div class="no-data">No report data available</div>
    <?php endif; ?>
</body>
</html>
```

## Database Schema Requirements

### Core Tables
```sql
-- Member agreements
CREATE TABLE abc_members_agreements (
    id INT PRIMARY KEY AUTO_INCREMENT,
    member_id INT,
    club_id INT,
    agreementNumber VARCHAR(50),
    membershipType VARCHAR(50),
    currentQueue VARCHAR(50),
    salesPersonName VARCHAR(100),
    signDate DATE,
    lastRewriteDate DATE,
    sinceDate DATE,
    nextBillingDate DATE,
    nextDueAmount DECIMAL(10,2),
    agreementPaymentMethod VARCHAR(50),
    paymentPlan_id INT,
    agreementEntrySource VARCHAR(50)
);

-- Member personal data
CREATE TABLE abc_members_personaldata (
    member_id INT PRIMARY KEY,
    firstName VARCHAR(50),
    lastName VARCHAR(50),
    email VARCHAR(100),
    primaryPhone VARCHAR(20),
    mobilePhone VARCHAR(20),
    memberStatus_id INT,
    memberStatusReason TEXT,
    memberStatusDate DATE,
    firstCheckInTimestamp DATETIME,
    lastCheckInTimestamp DATETIME,
    totalCheckInCount INT
);

-- Check-ins
CREATE TABLE abc_checkins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    member_id_local INT,
    checkin_club_id_local INT,
    checkInTimeStamp DATETIME,
    INDEX idx_checkin_timestamp (checkInTimeStamp),
    INDEX idx_checkin_club (checkin_club_id_local)
);

-- Member statuses
CREATE TABLE abc_member_statuses (
    id INT PRIMARY KEY,
    name VARCHAR(50),
    active TINYINT(1),
    cancel TINYINT(1)
);

-- Payment plans
CREATE TABLE abc_payment_plans (
    id INT PRIMARY KEY,
    name VARCHAR(100),
    EFT TINYINT(1),
    Employee TINYINT(1),
    upgraded TINYINT(1),
    Downgraded TINYINT(1)
);

-- Clubs
CREATE TABLE abc_clubs (
    id INT PRIMARY KEY,
    clubNumber INT,
    name VARCHAR(100),
    club_name VARCHAR(100)
);
```

## Implementation Steps

### 1. Environment Setup
1. Install CodeIgniter framework
2. Configure database connections
3. Set up mPDF library for PDF generation
4. Configure SMTP settings for email distribution

### 2. Core Files Implementation
1. Create Report_lib base class
2. Implement Club_report library
3. Add Report_model methods
4. Create Dashboard_model with all query methods
5. Set up Email service controller
6. Create PDF template view

### 3. Configuration
1. Add report configuration to ut_reports.php
2. Set up automated_reports database tables
3. Configure email distribution lists
4. Set up cron job scheduling

### 4. Testing
1. Test individual query methods
2. Verify PDF generation
3. Test email distribution
4. Validate cron job execution

### 5. Deployment
1. Set up production database
2. Configure production SMTP
3. Schedule cron jobs
4. Monitor report generation

## Dependencies

### Required Libraries
- **CodeIgniter 3.x**: PHP MVC framework
- **mPDF**: PDF generation library
- **PHPMailer**: Email sending (if not using CI email library)

### Database Requirements
- MySQL 5.7+ or MariaDB 10.2+
- Proper indexing on large tables (abc_checkins, abc_members_agreements)
- Regular maintenance for optimal performance

### Server Requirements
- PHP 7.4+ with required extensions
- Sufficient memory for PDF generation (512MB+ recommended)
- Cron job scheduling capability
- SMTP server access for email distribution

This comprehensive guide provides everything needed to implement the club_report system in another environment, including all database queries, configuration files, templates, and implementation steps.
