# Automated Reports System - Comprehensive Documentation

## Overview
The Undefeated Tribe system implements a sophisticated automated reporting infrastructure that generates, schedules, and distributes various business reports via email. The system supports multiple report formats (PDF, Excel, CSV) and handles complex scheduling patterns.

## System Architecture

### Core Components

#### 1. Database Tables
- **`automated_reports`**: Main configuration table storing report definitions
  - Fields: `id`, `type`, `description`, `subject`, `body`, `frequency`, scheduling fields (`minute`, `hour`, `day_of_month`, `month`, `day_of_week`), `from_label_id`, `active`, timestamps
  - Supports frequencies: every minute, hourly, daily, weekly, monthly, yearly, custom

- **`automated_reports_distribution`**: Manages email distribution lists
  - Fields: `id`, `type` (to/bcc), `report_id`, `club_id`, `region_id`, `user_id`, `group_id`, `email`
  - Supports distribution by club, region, user groups, or direct email addresses

- **`automated_reports_from_labels`**: Defines sender labels for reports

#### 2. Core Libraries

##### Email_reports Library (`application/libraries/Email_reports.php`)
- **Purpose**: Central email configuration and distribution management
- **Key Methods**:
  - `get_settings()`: Retrieves report configuration and recipient lists
  - `_get_report_distribution()`: Determines email recipients based on club/region/user assignments
- **Features**:
  - Environment-based email routing (production vs test)
  - Dynamic recipient resolution based on user permissions and club assignments
  - SMTP server configuration management

##### Report_lib Base Class (`application/libraries/reports/Report_lib.php`)
- **Purpose**: Base class for all report implementations
- **Key Methods**:
  - `send_report()`: Main method for sending reports via email
  - `send_email()`: Handles actual email transmission
  - `_get_club_name()`, `_get_region_name()`: Utility methods for name resolution
- **Features**:
  - Standardized error handling and success messaging
  - Club parameter validation
  - CLI output support

#### 3. Report Generation Models

##### Report_model (`application/models/admin/Report_model.php`)
- **Purpose**: Contains 55+ report generation methods
- **Key Report Types**:
  - Marketing reports (active members, unique emails, how heard analysis)
  - Financial reports (cancelled members, credit card expiring, payment plans)
  - Operational reports (upgrade opportunities, prospect lists, product sales)
  - KPI reports (marketing, PT, group fitness, NCO)
  - PDF reports (club productivity, contest scorecards, termination reports)

##### Report_kpi_model (`application/models/admin/Report_kpi_model.php`)
- **Purpose**: Specialized KPI report generation with advanced formatting
- **Features**:
  - Excel report generation with complex styling
  - PDF report generation with mPDF library
  - Image compression for PDF reports
  - Multi-sheet Excel workbooks
  - Dynamic data chunking for large datasets

## Report Types and Categories

### 1. Daily Reports
- **Club Reports**: New guests, members, ACH returns, cancellations
- **Business Reports**: MP, Market Partner, Regional VP, Company-wide
- **PT Reports**: Personal training metrics and activities
- **Group Fitness Reports**: Class utilization, exceptions, KPIs
- **Data Quality Reports**: Marketing and operations data validation

### 2. Weekly Reports
- **Revenue Reports**: Financial performance summaries
- **Exception Reports**: Club-specific operational exceptions
- **Opportunity Reports**: Upgrade and membership opportunities
- **Payroll Reports**: Group fitness and PT payroll data

### 3. Monthly Reports
- **Reward Reports**: Monthly reward calculations
- **Contest Reports**: Performance contest results
- **Facebook Reports**: Social media analytics
- **Exception Reports**: Monthly operational exceptions

### 4. Specialized Reports
- **Financial Reports**: Direct bill clients, inventory adjustments, annual fee mismatches
- **Compliance Reports**: Kids Crunch audit, Temple ISD member lists
- **Contest Reports**: Various promotional contest scorecards
- **Maintenance Reports**: TruAsset data maintenance, lamp usage

## Scheduling and Execution

### Cron Job Infrastructure
Located in `/CRONs/` directory with multiple scheduling scripts:

#### Main Batch Scripts
- **`batch_run_all_reports.sh`**: Master script running every 10 minutes
- **`batch_run_reports.sh`**: Alternative execution script
- **`batch_run_all.sh`**: Comprehensive data sync and reporting

#### Scheduling Pattern
```bash
*/10 * * * * batch_run_all.sh  # Every 10 minutes
0 7 * * * batch_daily_emails_all.sh  # Daily at 7 AM
2 7 * * 1 weekly_revenue_report.sh  # Weekly Monday at 7:02 AM
0 8,20 * * * batch_daily_8pm.sh  # Twice daily at 8 AM/PM
```

#### Report-Specific Scripts
Individual shell scripts in `/CRONs/emails/` for specific reports:
- `daily_club_report.sh`
- `daily_mp_club_report.sh`
- `daily_groupfit_exceptions.sh`
- `weekly_revenue_report.sh`
- And 40+ other specialized report scripts

### Execution Flow
1. **Data Updates**: KPI scripts update underlying data
2. **Dependency Checks**: Ensures data freshness before report generation
3. **Report Generation**: Sequential execution with sleep intervals
4. **Email Distribution**: Automated sending to configured recipients
5. **Logging**: Comprehensive logging to `/logs/` directories

## Data Sources and Integration

### Primary Data Sources
- **ABC Financial**: Member data, billing information, transactions
- **Club Management System**: Check-ins, member activities, staff data
- **Marketing Systems**: Campaign data, lead tracking, conversion metrics
- **Group Fitness**: Class schedules, attendance, instructor data
- **Personal Training**: Session data, trainer metrics, revenue
- **Point of Sale**: Product sales, inventory data
- **Financial Systems**: Payment processing, returns, chargebacks

### Data Synchronization
- **Real-time Sync**: Member updates, check-ins, transactions
- **Batch Processing**: Nightly data imports and reconciliation
- **FTP Integration**: Automated file processing for external systems
- **API Integration**: Direct system-to-system data exchange

## Template and Formatting System

### PDF Generation
- **mPDF Library**: Primary PDF generation engine
- **Template System**: PHP-based templates in `/application/views/pdf/`
- **Styling**: CSS-based formatting with responsive design
- **Image Compression**: Automatic image optimization for PDF size reduction
- **Multi-format Support**: A4 Portrait/Landscape, custom page sizes

### Excel Generation
- **PHPExcel Library**: Excel file creation and formatting
- **Advanced Styling**: Cell formatting, colors, borders, fonts
- **Multi-sheet Support**: Complex workbooks with multiple data sheets
- **Formula Support**: Calculated fields and summary formulas
- **Data Chunking**: Memory-efficient processing of large datasets

### Email Templates
- **HTML Templates**: Rich formatting for email bodies
- **Variable Substitution**: Dynamic content insertion
- **Attachment Handling**: Multiple file attachments per email
- **SMTP Configuration**: Environment-specific email server settings

## Configuration and Management

### Admin Interface
- **Automated Reports Controller**: `/admin/automated_reports`
- **CRUD Operations**: Create, read, update, delete report configurations
- **Distribution Management**: Configure recipient lists and permissions
- **Scheduling Interface**: Set up complex scheduling patterns
- **Testing Features**: Test mode for development and validation

### Environment Configuration
- **Email Settings**: SMTP servers, authentication, from addresses
- **Database Configuration**: Connection strings, credentials
- **File Paths**: Export directories, temporary file locations
- **Feature Flags**: Enable/disable specific report types

## Security and Access Control

### Permission System
- **Role-based Access**: User groups determine report access
- **Club-based Filtering**: Users see only their assigned club data
- **Region-based Access**: Regional managers see regional data
- **Email Validation**: Production vs test email routing

### Data Protection
- **Environment Isolation**: Test emails in non-production environments
- **Audit Logging**: Comprehensive logging of report generation and distribution
- **Error Handling**: Graceful failure handling with notification
- **Data Sanitization**: Input validation and SQL injection prevention

## Performance and Scalability

### Optimization Features
- **Memory Management**: Efficient handling of large datasets
- **Execution Time Limits**: Configurable timeouts for long-running reports
- **Chunked Processing**: Breaking large reports into manageable pieces
- **Caching**: Strategic caching of frequently accessed data
- **Queue System**: Background job processing with Beanstalk

### Monitoring and Maintenance
- **Log Analysis**: Detailed logging for troubleshooting
- **Performance Metrics**: Execution time tracking
- **Error Notification**: Automated alerts for failed reports
- **Health Checks**: System status monitoring

## Integration Points

### External Systems
- **ABC Financial**: Primary billing and member management system
- **Woven API**: Location and club data management
- **HubSpot**: Marketing automation and lead tracking
- **UKG**: Employee and payroll data
- **GroupX/Hartl**: Group fitness class management

### Internal Systems
- **Dashboard System**: Real-time KPI display
- **User Management**: Authentication and authorization
- **Club Management**: Multi-location data handling
- **Marketing Tools**: Campaign tracking and analysis

This comprehensive system handles dozens of report types, serves multiple user roles, and processes thousands of data points daily to provide critical business intelligence to the Undefeated Tribe organization.

## Detailed Report Implementations

### 1. Club Report System
**Location**: `application/libraries/reports/Club_report.php`
**Purpose**: Daily operational reports for individual clubs
**Data Sources**:
- New guest check-ins and missed opportunities
- New member acquisitions
- ACH returns and chargebacks
- Credit card processing issues
- Member cancellations and terminations

**Report Sections**:
1. **First Time Missed Guests**: Guests who checked in but weren't properly processed
2. **New Members**: Daily membership acquisitions with source tracking
3. **ACH Returns**: Failed automatic payments requiring follow-up
4. **Credit Card Returns**: Declined card transactions
5. **Cancels Processed**: Member terminations with reasons
6. **Member Cancel Report**: Detailed cancellation analysis

### 2. Business Intelligence Reports
**MP Club Report** (`Mp_club_report.php`):
- Monthly-to-date rankings across all clubs
- EFT (Electronic Funds Transfer) sales performance
- Budget vs actual performance metrics
- New member acquisition (NMA) tracking
- Peak membership percentage analysis

**Market Partner Report** (`Mktp_club_report.php`):
- Regional performance aggregation
- Market-level KPI tracking
- Competitive analysis between regions
- Revenue performance by market

**Regional VP Report** (`Rvp_club_report.php`):
- Executive-level performance summaries
- Multi-region comparative analysis
- Strategic KPI tracking
- High-level business metrics

### 3. KPI Report Generation
**Marketing KPI Report**:
- Lead generation metrics
- Conversion rate analysis
- Campaign performance tracking
- Cost per acquisition calculations
- Source attribution analysis

**Personal Training KPI Report**:
- Session utilization rates
- Trainer performance metrics
- Revenue per trainer analysis
- Client retention rates
- Package sales performance

**Group Fitness KPI Report**:
- Class utilization percentages
- Cost per head (CPH) calculations
- Instructor performance metrics
- Peak membership engagement
- Hot yoga and specialty class analytics

### 4. Financial Reports
**Revenue Report** (`Revenue.php`):
- Daily revenue summaries
- Payment method breakdowns
- Refund and chargeback tracking
- Monthly recurring revenue (MRR) analysis

**Direct Bill Report** (`Direct_bill.php`):
- Corporate account management
- B2B client tracking
- Special billing arrangement monitoring

**Annual Fee Mismatch** (`Annual_fee_mismatch.php`):
- Billing discrepancy identification
- Fee correction requirements
- Audit trail maintenance

### 5. Exception and Quality Reports
**Club Exception Report**:
- Payment type exceptions requiring manual review
- Unfrozen members with billing date issues
- Guest check-in anomalies (10+ visits without proper classification)
- Active members with future billing dates (59+ days)

**Data Quality Reports**:
- Marketing data validation and cleanup requirements
- Operations data integrity checks
- Missing or inconsistent data identification
- Data enrichment opportunities

## Technical Implementation Details

### Report Generation Workflow
1. **Data Collection**: SQL queries aggregate data from multiple tables
2. **Data Processing**: Business logic applies calculations and transformations
3. **Format Generation**: Data is formatted into Excel, PDF, or CSV
4. **Template Application**: Styling and layout templates are applied
5. **File Creation**: Reports are saved to `/exports/` directories
6. **Email Preparation**: Recipients are determined and email content prepared
7. **Distribution**: Reports are sent via SMTP with attachments
8. **Cleanup**: Temporary files are removed and logs updated

### Database Query Patterns
Reports typically use complex JOIN operations across tables:
- `abc_members`: Core member information
- `abc_clubs`: Club details and settings
- `abc_transactions`: Financial transaction data
- `abc_checkins`: Member visit tracking
- `abc_employees`: Staff and trainer information
- `abc_classes`: Group fitness scheduling
- `abc_pt_sessions`: Personal training data

### Error Handling and Recovery
- **Graceful Degradation**: Reports continue with partial data if some sources fail
- **Retry Logic**: Failed email sends are retried with exponential backoff
- **Error Notification**: System administrators receive alerts for critical failures
- **Fallback Recipients**: Test email addresses used in non-production environments
- **Transaction Safety**: Database operations use transactions to ensure consistency

### Performance Optimization
- **Query Optimization**: Indexed database queries with optimized JOIN operations
- **Memory Management**: Large datasets processed in chunks to prevent memory exhaustion
- **Caching Strategy**: Frequently accessed data cached to reduce database load
- **Parallel Processing**: Independent reports generated concurrently where possible
- **Resource Limits**: Execution time and memory limits prevent runaway processes

### File Management
- **Export Directories**: Organized by report type and date
- **Cleanup Policies**: Automated removal of old report files
- **Backup Strategy**: Critical reports archived for compliance
- **Access Control**: File system permissions restrict access to sensitive data

## Monitoring and Troubleshooting

### Logging System
**Log Categories**:
- **Daily Emails**: `/logs/daily_emails/` - Standard report execution logs
- **Weekly Emails**: `/logs/weekly_emails/` - Weekly report processing
- **Update KPI**: `/logs/update_kpi_daily_data/` - KPI calculation logs
- **Fetch Events**: `/logs/fetch_events/` - Data synchronization logs
- **Error Logs**: Application-level error tracking

**Log Format**: Timestamped entries with execution details, parameters, and results

### Health Monitoring
- **Execution Time Tracking**: Reports monitored for performance degradation
- **Success Rate Monitoring**: Failed report generation tracked and alerted
- **Data Freshness Checks**: Ensures underlying data is current before report generation
- **Recipient Validation**: Email address validation and bounce handling
- **System Resource Monitoring**: CPU, memory, and disk usage tracking

### Troubleshooting Tools
- **Manual Report Triggers**: Admin interface allows manual report execution
- **Test Mode**: Reports can be generated with test recipients for validation
- **Debug Output**: Detailed execution information available for troubleshooting
- **Configuration Validation**: System checks for proper setup and permissions
- **Data Integrity Checks**: Validation of source data before report generation

This system represents a mature, enterprise-level reporting infrastructure capable of handling complex business intelligence requirements while maintaining reliability, security, and performance standards.

## Complete Report Inventory

### Daily Automated Reports

#### Club Operations Reports
1. **Club Report** (`club_report`)
   - **Frequency**: Daily at 7:00 AM
   - **Recipients**: Club managers, regional staff
   - **Content**: New guests, members, ACH returns, cancellations
   - **Format**: Excel with multiple sheets
   - **Data Sources**: abc_members, abc_checkins, abc_transactions

2. **MP Club Report** (`mp_club_report`)
   - **Frequency**: Daily at 7:00 AM
   - **Recipients**: Market Partners, Regional VPs
   - **Content**: MTD rankings, EFT sales, budget performance
   - **Format**: Excel with conditional formatting
   - **Data Sources**: abc_clubs, abc_transactions, budget tables

3. **Market Partner Report** (`mktp_club_report`)
   - **Frequency**: Daily at 7:00 AM
   - **Recipients**: Market Partners, Corporate staff
   - **Content**: Regional aggregations, market comparisons
   - **Format**: Excel with charts and graphs
   - **Data Sources**: Multi-club aggregated data

4. **Regional VP Report** (`rvp_club_report`)
   - **Frequency**: Daily at 7:00 AM
   - **Recipients**: Regional VPs, Executive team
   - **Content**: High-level KPIs, strategic metrics
   - **Format**: Executive summary Excel format
   - **Data Sources**: Regional aggregated data

5. **Company Business Report** (`compbusiness_club_report`)
   - **Frequency**: Daily at 7:00 AM
   - **Recipients**: Corporate executives, Finance team
   - **Content**: Company-wide performance metrics
   - **Format**: Comprehensive Excel workbook
   - **Data Sources**: All club data aggregated

#### Personal Training Reports
6. **PT Report** (`pt_report`)
   - **Frequency**: Daily at 7:00 AM
   - **Recipients**: PT managers, Club managers
   - **Content**: Session utilization, trainer performance
   - **Format**: Excel with trainer-specific sheets
   - **Data Sources**: abc_pt_sessions, abc_employees

7. **PT Active/Inactive Report** (`pt_active_inactive_report`)
   - **Frequency**: Weekly
   - **Recipients**: PT Directors, Regional managers
   - **Content**: Trainer status, session counts
   - **Format**: Excel with status indicators
   - **Data Sources**: abc_employees, abc_pt_sessions

#### Group Fitness Reports
8. **Group Fitness Exceptions** (`groupfit_exceptions`)
   - **Frequency**: Daily at 7:00 AM
   - **Recipients**: GF coordinators, Club managers
   - **Content**: Class utilization issues, instructor problems
   - **Format**: Excel with exception highlighting
   - **Data Sources**: abc_classes, abc_checkins

9. **Group Fitness Payroll** (`groupfit_payroll`)
   - **Frequency**: Weekly
   - **Recipients**: Payroll department, GF managers
   - **Content**: Instructor hours, class payments
   - **Format**: Excel formatted for payroll processing
   - **Data Sources**: abc_classes, abc_employees

#### KPI and Analytics Reports
10. **Marketing KPI Report** (`marketing_kpi_report`)
    - **Frequency**: Daily at 8:00 AM
    - **Recipients**: Marketing team, Regional managers
    - **Content**: Lead metrics, conversion rates, campaign performance
    - **Format**: Multi-sheet Excel with charts
    - **Data Sources**: abc_members, abc_leads, marketing tables

11. **PT KPI Report** (`pt_kpi_report`)
    - **Frequency**: Daily at 8:00 AM
    - **Recipients**: PT Directors, Club managers
    - **Content**: Revenue per trainer, session utilization
    - **Format**: Excel with performance indicators
    - **Data Sources**: abc_pt_sessions, revenue tables

12. **Group Fitness KPI Report** (`gf_kpi_report`)
    - **Frequency**: Daily at 8:00 AM
    - **Recipients**: GF Directors, Club managers
    - **Content**: Class utilization, cost per head
    - **Format**: Excel with utilization charts
    - **Data Sources**: abc_classes, abc_checkins

13. **NCO KPI Report** (`nco_kpi_report`)
    - **Frequency**: Daily at 8:00 AM
    - **Recipients**: NCO managers, Regional staff
    - **Content**: New member acquisition metrics
    - **Format**: Excel with acquisition tracking
    - **Data Sources**: abc_members, abc_transactions

#### Data Quality Reports
14. **Marketing Data Quality Report** (`marketing_data_quality_report`)
    - **Frequency**: Daily at 8:00 AM
    - **Recipients**: Marketing team, Data analysts
    - **Content**: Data completeness, validation errors
    - **Format**: Excel with data quality metrics
    - **Data Sources**: abc_members, abc_leads

15. **Operations Data Quality Report** (`ops_data_quality_report`)
    - **Frequency**: Daily at 8:00 AM
    - **Recipients**: Operations team, Club managers
    - **Content**: Operational data validation
    - **Format**: Excel with error highlighting
    - **Data Sources**: Multiple operational tables

### Weekly Automated Reports

#### Financial Reports
16. **Revenue Report** (`revenue_report`)
    - **Frequency**: Weekly on Mondays at 7:02 AM
    - **Recipients**: Finance team, Executive staff
    - **Content**: Weekly revenue summaries, trends
    - **Format**: Excel with financial charts
    - **Data Sources**: abc_transactions, revenue tables

17. **Direct Bill Report** (`direct_bill`)
    - **Frequency**: Weekly
    - **Recipients**: Billing department, Account managers
    - **Content**: Corporate account billing
    - **Format**: Excel formatted for billing processing
    - **Data Sources**: abc_direct_bill, abc_transactions

#### Exception Reports
18. **Club Exception Report** (`club_exception_report`)
    - **Frequency**: Weekly
    - **Recipients**: Club managers, Regional staff
    - **Content**: Payment exceptions, billing anomalies
    - **Format**: Excel with exception categories
    - **Data Sources**: abc_transactions, abc_members

19. **Inventory Adjustments** (`inventory_adjustments`)
    - **Frequency**: Weekly
    - **Recipients**: Inventory managers, Club managers
    - **Content**: Stock adjustments, discrepancies
    - **Format**: Excel with adjustment tracking
    - **Data Sources**: inventory tables

20. **Annual Fee Mismatch** (`annual_fee_mismatch`)
    - **Frequency**: Weekly
    - **Recipients**: Billing department, Finance team
    - **Content**: Fee discrepancies requiring correction
    - **Format**: Excel with mismatch details
    - **Data Sources**: abc_members, billing tables

### Monthly Automated Reports

#### Performance Reports
21. **Monthly Reward Report** (`monthly_reward_report`)
    - **Frequency**: Monthly on 1st at 8:00 AM
    - **Recipients**: HR department, Club managers
    - **Content**: Employee reward calculations
    - **Format**: Excel with reward breakdowns
    - **Data Sources**: performance tables, abc_employees

22. **Contest Scorecard Reports** (Multiple contest types)
    - **Frequency**: Monthly
    - **Recipients**: Contest participants, Managers
    - **Content**: Contest performance metrics
    - **Format**: PDF scorecards
    - **Data Sources**: Contest-specific tables

#### Compliance Reports
23. **Kids Crunch Audit** (`kids_crunch_audit`)
    - **Frequency**: Monthly
    - **Recipients**: Compliance team, Legal department
    - **Content**: Child care program compliance
    - **Format**: Excel with compliance metrics
    - **Data Sources**: kids_crunch tables

24. **Temple ISD Member List** (`temple_isd_member_list`)
    - **Frequency**: Monthly
    - **Recipients**: Partnership managers
    - **Content**: Educational partnership member tracking
    - **Format**: Excel member list
    - **Data Sources**: abc_members with ISD filters

### Specialized and Ad-hoc Reports

#### Financial Analysis
25. **Projected Financial Terminations** (`projected_financial_terminations`)
    - **Purpose**: Forecast member terminations based on payment patterns
    - **Format**: Excel with projection models
    - **Data Sources**: abc_transactions, abc_members

26. **Marketing Budget Report** (`marketing_budget_report`)
    - **Purpose**: Marketing spend analysis and budget tracking
    - **Format**: Excel with budget comparisons
    - **Data Sources**: marketing_budget tables

#### Operational Analysis
27. **Upgrade Opportunities Report** (`upgrade_opportunities`)
    - **Purpose**: Identify members eligible for membership upgrades
    - **Format**: Excel with opportunity scoring
    - **Data Sources**: abc_members, usage patterns

28. **Prospect List Report** (`prospect_list`)
    - **Purpose**: Generate targeted prospect lists for marketing
    - **Format**: Excel with contact information
    - **Data Sources**: abc_leads, abc_members

29. **Product Sales Report** (`product_sales`)
    - **Purpose**: Track retail and service sales performance
    - **Format**: Excel with sales analytics
    - **Data Sources**: pos_transactions, inventory tables

#### Maintenance and Technical Reports
30. **TruAsset Data Maintenance** (`truasset_data_maintenance`)
    - **Purpose**: Equipment and asset tracking maintenance
    - **Format**: Excel with maintenance schedules
    - **Data Sources**: truasset integration tables

31. **Lamp Usage Report** (`lamp_usage`)
    - **Purpose**: Tanning bed lamp usage tracking
    - **Format**: Excel with usage metrics
    - **Data Sources**: tanning equipment tables

32. **Facebook Report** (`facebook_report`)
    - **Purpose**: Social media analytics and engagement
    - **Format**: Excel with social metrics
    - **Data Sources**: facebook_api integration

This comprehensive inventory represents over 30 distinct report types, each serving specific business intelligence needs across operations, finance, marketing, and compliance functions.

## Configuration Examples

### Database Configuration Example
```sql
-- Example automated report configuration
INSERT INTO automated_reports (
    type,
    description,
    subject,
    body,
    frequency,
    hour,
    minute,
    from_label_id,
    active
) VALUES (
    'club_report',
    'Daily Club Operations Report',
    'Daily Club Report - {club_name} - {date}',
    'Please find attached the daily operations report for your club.',
    'daily',
    '7',
    '0',
    1,
    1
);

-- Example distribution configuration
INSERT INTO automated_reports_distribution (
    type,
    report_id,
    club_id,
    user_id
) VALUES (
    'to',
    1,
    101,
    NULL  -- All users assigned to club 101
);
```

### Cron Job Configuration Example
```bash
# Main batch processing - runs every 10 minutes
*/10 * * * * /var/www/login.undefeatedtribe.com/CRONs/batch_run_all.sh

# Daily reports at 7:00 AM
0 7 * * * /var/www/login.undefeatedtribe.com/CRONs/emails/batch_daily_emails_all.sh

# Weekly revenue report - Mondays at 7:02 AM
2 7 * * 1 /var/www/login.undefeatedtribe.com/CRONs/emails/weekly_revenue_report.sh

# Twice daily reports at 8 AM and 8 PM
0 8,20 * * * /var/www/login.undefeatedtribe.com/CRONs/emails/batch_daily_8pm.sh
```

### Email Configuration Example
```php
// SMTP Configuration in Email_reports library
$config = array(
    'protocol' => 'smtp',
    'smtp_host' => getenv('SMTP_HOST'),
    'smtp_port' => getenv('SMTP_PORT'),
    'smtp_user' => getenv('SMTP_USER'),
    'smtp_pass' => getenv('SMTP_PASS'),
    'smtp_crypto' => 'tls',
    'mailtype' => 'html',
    'charset' => 'utf-8',
    'newline' => "\r\n"
);
```

### Report Library Implementation Example
```php
// Example report library structure
class Club_report extends Report_lib {

    public function send_club_report($club_id = null) {
        // 1. Validate parameters
        if (!$this->_validate_club($club_id)) {
            return false;
        }

        // 2. Generate report data
        $report_data = $this->_generate_report_data($club_id);

        // 3. Create Excel file
        $filename = $this->_create_excel_report($report_data, $club_id);

        // 4. Send email with attachment
        return $this->send_report('club_report', $club_id, $filename);
    }

    private function _generate_report_data($club_id) {
        // Complex data aggregation logic
        return $this->report_model->club_report($club_id);
    }
}
```

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. Reports Not Generating
**Symptoms**: Scheduled reports fail to execute
**Possible Causes**:
- Cron job not running
- Database connection issues
- Insufficient disk space
- PHP memory limits exceeded

**Solutions**:
```bash
# Check cron job status
crontab -l
systemctl status cron

# Check disk space
df -h /var/www/login.undefeatedtribe.com/exports/

# Check PHP memory limits
php -i | grep memory_limit

# Check database connectivity
mysql -u username -p -h hostname database_name
```

#### 2. Email Delivery Failures
**Symptoms**: Reports generate but emails not received
**Possible Causes**:
- SMTP server issues
- Authentication failures
- Recipient email validation
- Attachment size limits

**Solutions**:
```php
// Test SMTP configuration
$this->load->library('email');
$config = $this->email_reports->get_smtp_config();
$this->email->initialize($config);

// Check email logs
tail -f /var/log/mail.log

// Validate recipient addresses
$this->email_reports->validate_recipients($report_id);
```

#### 3. Data Quality Issues
**Symptoms**: Reports contain incorrect or missing data
**Possible Causes**:
- Data synchronization delays
- Database query timeouts
- Incomplete data imports
- Timezone issues

**Solutions**:
```sql
-- Check data freshness
SELECT MAX(updated_at) FROM abc_members;
SELECT MAX(created_at) FROM abc_transactions WHERE DATE(created_at) = CURDATE();

-- Verify data integrity
SELECT COUNT(*) FROM abc_members WHERE club_id IS NULL;
SELECT COUNT(*) FROM abc_transactions WHERE amount = 0;
```

#### 4. Performance Issues
**Symptoms**: Reports take too long to generate or timeout
**Possible Causes**:
- Large dataset processing
- Inefficient database queries
- Memory exhaustion
- Concurrent report generation

**Solutions**:
```php
// Implement chunked processing
$chunk_size = 1000;
$offset = 0;
do {
    $data_chunk = $this->get_data_chunk($offset, $chunk_size);
    $this->process_chunk($data_chunk);
    $offset += $chunk_size;
} while (count($data_chunk) == $chunk_size);

// Optimize database queries
// Add appropriate indexes
// Use LIMIT and OFFSET for pagination
// Implement query caching
```

### Monitoring and Alerts

#### Log File Locations
```bash
# Daily email logs
/var/www/login.undefeatedtribe.com/logs/daily_emails/

# Weekly email logs
/var/www/login.undefeatedtribe.com/logs/weekly_emails/

# KPI update logs
/var/www/login.undefeatedtribe.com/logs/update_kpi_daily_data/

# Application error logs
/var/www/login.undefeatedtribe.com/application/logs/
```

#### Health Check Commands
```bash
# Check recent report executions
grep "SUCCESS" /var/www/login.undefeatedtribe.com/logs/daily_emails/*.log | tail -20

# Check for errors
grep "ERROR\|FAILED" /var/www/login.undefeatedtribe.com/logs/daily_emails/*.log | tail -10

# Monitor disk usage
du -sh /var/www/login.undefeatedtribe.com/exports/*

# Check database connections
mysqladmin -u username -p processlist
```

### Maintenance Procedures

#### Daily Maintenance
1. **Log Review**: Check error logs for failures
2. **Disk Cleanup**: Remove old export files
3. **Performance Check**: Monitor execution times
4. **Data Validation**: Verify data freshness

#### Weekly Maintenance
1. **Cron Job Audit**: Verify all scheduled jobs are running
2. **Email Delivery Review**: Check bounce rates and delivery failures
3. **Database Optimization**: Update statistics and optimize queries
4. **Backup Verification**: Ensure report configurations are backed up

#### Monthly Maintenance
1. **Performance Analysis**: Review execution time trends
2. **Capacity Planning**: Monitor storage and processing requirements
3. **Security Review**: Update credentials and access permissions
4. **Documentation Update**: Keep configuration documentation current

This comprehensive documentation provides complete coverage of the Automated Reports system, from high-level architecture to detailed troubleshooting procedures, enabling effective management and maintenance of this critical business intelligence infrastructure.
