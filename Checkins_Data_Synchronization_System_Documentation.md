# Check-ins Data Synchronization System - Complete Implementation Guide

## Overview
The Check-ins Data Synchronization System is a comprehensive automated solution that fetches member check-in data from ABC Financial's API and synchronizes it with the local UndefeatedTribe database. The system handles both incoming check-ins (members from other clubs checking into local clubs) and outgoing check-ins (local club members checking into other clubs).

## System Architecture

### Core Components
1. **Shell Scripts**: Automated cron-triggered scripts for data fetching and synchronization
2. **Service Controllers**: PHP controllers handling API calls and data processing
3. **API Models**: Interface layer for ABC Financial API communication
4. **Sync Models**: Database synchronization and tracking management
5. **Checkins Models**: Data processing and database operations
6. **Database Tables**: Storage for raw data, processed data, and sync tracking

### Data Flow Overview
```
ABC Financial API → Shell Scripts → Service Controllers → API Models → Raw Data Storage → Processing → Final Database Tables
```

## Shell Scripts Infrastructure

### 1. fetch_remote_checkins.sh
**Purpose**: Fetches check-in data from ABC Financial API via HTTP calls
**Location**: `CRONs/fetch_remote_checkins.sh`

```bash
#!/bin/sh

cd "$(dirname "$0")"
. ./env_settings/env_settings

dir_name=$(TZ='America/Chicago' date +'%Y-%m-%d')
log_base_path="$LOCAL_PATH/logs/fetch_checkins"
full_path="$log_base_path/$dir_name"
mkdir -p "$full_path"

echo "Incoming"
curl -k $HOSTNAME/services/fetch_all_checkins_comingtoclub?db_source=$DB_SOURCE > "$full_path/fetch_checkins_all_incoming_$(TZ='America/Chicago' date +'%Y_%m_%d_%H_%M_%S').log"

echo "Outgoing"
curl -k $HOSTNAME/services/fetch_all_checkins_membersofclub_to_foreign?db_source=$DB_SOURCE > "$full_path/fetch_checkins_members_to_foreign_$(TZ='America/Chicago' date +'%Y_%m_%d_%H_%M_%S').log"
```

**Key Features**:
- **Environment Configuration**: Sources settings from `env_settings/env_settings`
- **Organized Logging**: Creates date-based log directories
- **Dual Data Streams**: Handles both incoming and outgoing check-ins
- **Timestamped Logs**: Each execution creates uniquely named log files

### 2. fetch_remote_checkins_cli.sh
**Purpose**: CLI version using PHP direct execution instead of HTTP calls
**Location**: `CRONs/fetch_remote_checkins_cli.sh`

```bash
#!/bin/sh

cd "$(dirname "$0")"
. ./env_settings/env_settings
cd ..

dir_name=$(TZ='America/Chicago' date +'%Y-%m-%d')
log_base_path="$LOCAL_PATH/logs/fetch_checkins"
full_path="$log_base_path/$dir_name"
mkdir -p "$full_path"

echo "Incoming"
php index.php services/abcfinancial/fetch_all_checkins_coming_to_club > "$full_path/fetch_checkins_all_incoming_$(TZ='America/Chicago' date +'%Y_%m_%d_%H_%M_%S').log"

echo "Outgoing"
php index.php services/abcfinancial/fetch_all_checkins_members_of_club_to_foreign > "$full_path/fetch_checkins_members_to_foreign_$(TZ='America/Chicago' date +'%Y_%m_%d_%H_%M_%S').log"
```

**Advantages over HTTP version**:
- **Direct PHP Execution**: Bypasses web server overhead
- **Better Error Handling**: Direct access to PHP error reporting
- **Resource Efficiency**: No HTTP request/response overhead

### 3. update_checkins.sh
**Purpose**: Processes fetched raw data into final database tables
**Location**: `CRONs/update_checkins.sh`

```bash
#!/bin/sh

cd "$(dirname "$0")"
. ./env_settings/env_settings

curl -k $HOSTNAME/services/update_checkins_data?db_source=$DB_SOURCE > $LOCAL_PATH/logs/update_checkins/update_checkins_$(TZ='America/Chicago' date +'%Y_%m_%d_%H_%M_%S').log
```

### 4. update_checkins_cli.sh
**Purpose**: CLI version of data processing
**Location**: `CRONs/update_checkins_cli.sh`

```bash
#!/bin/sh

cd "$(dirname "$0")"
. ./env_settings/env_settings
cd ..

php index.php services/abcfinancial/update_checkins_full > $LOCAL_PATH/logs/update_checkins/update_checkins_$(TZ='America/Chicago' date +'%Y_%m_%d_%H_%M_%S').log
```

## Environment Configuration

### Environment Settings
**File**: `CRONs/env_settings/env_settings_prod`

```bash
# Database and API Configuration
DB_SOURCE="production"
HOSTNAME="https://login.undefeatedtribe.com"
LOCAL_PATH="/var/www/login.undefeatedtribe.com"

# Timezone Configuration
TZ='America/Chicago'

# API Settings
API_TIMEOUT=300
MAX_RETRIES=3
```

**Key Variables**:
- **DB_SOURCE**: Database environment identifier
- **HOSTNAME**: Base URL for API calls
- **LOCAL_PATH**: Application root directory
- **TZ**: Timezone for log timestamps

## Service Controllers

### Abcfinancial Controller
**File**: `application/controllers/services/Abcfinancial.php`

#### 1. fetch_all_checkins_coming_to_club()
**Purpose**: Fetches check-ins of members from other clubs checking into local clubs

```php
public function fetch_all_checkins_coming_to_club() {
    $arrClubs = $this->abcfinancial_clubs_model->getAllClubs();
    
    echo '<B>FETCHING ALL IN-COMING CHECKINS FOR ['.count($arrClubs).'] CLUBS</B>';
    
    if(!empty($arrClubs)) {
        foreach ($arrClubs as $intClubId => $arrClubData) {
            $strClubId = $arrClubData['clubNumber'];
            echo '<BR><BR>Club ID: ' . $arrClubData['id'] . ' - number: '. $strClubId;
            
            // Get fetch period for this club
            $arrFetchPeriod = $this->abcfinancial_syncs_model->getUpdatePeriodCheckins($intClubId, 'coming_to_club');
            
            if (empty($arrFetchPeriod)) {
                continue;
            }
            
            // Create sync tracking record
            $intSyncId = $this->abcfinancial_syncs_model->createSyncCheckins($intClubId, 'coming_to_club', $arrFetchPeriod);
            
            // Fetch data from API
            $arrRemoteIncomingCheckins = array();
            if(!$this->config->item('abcfinancial_api_testmode')) {
                $arrRemoteIncomingCheckins = $this->abcfinancial_API_model->getRemoteCheckinsComingToClub($strClubId, $arrFetchPeriod['period_start'], $arrFetchPeriod['period_end']);
            }
            
            // Process each check-in record
            $intTotal = count($arrRemoteIncomingCheckins);
            $intSuccess = 0;
            
            foreach($arrRemoteIncomingCheckins as $arrOneRemoteIncomingCheckin) {
                $intRawCheckinId = $this->abcfinancial_checkins_model->insertRawIncomingCheckinData($intSyncId, $intClubId, $strClubId, $arrOneRemoteIncomingCheckin);
                
                if($intRawCheckinId) {
                    $intSuccess++;
                }
            }
            
            // Update sync status
            $strUpdateStatus = 'success';
            if($intTotal != $intSuccess) {
                $strUpdateStatus = 'failed';
            }
            $this->abcfinancial_syncs_model->finishSyncCheckins($intSyncId, $intTotal, $strUpdateStatus);
            
            echo '<BR>Checkins: Successfully added ['.$intSuccess.'] INCOMING checkins out of ['.$intTotal.']';
        }
    }
    
    die;
}
```

#### 2. fetch_all_checkins_members_of_club_to_foreign()
**Purpose**: Fetches check-ins of local club members checking into other clubs

```php
public function fetch_all_checkins_members_of_club_to_foreign()
{
    $arrClubs = $this->abcfinancial_clubs_model->getAllClubs();

    echo '<B>FETCHING ALL CHECKINS FOR ['.count($arrClubs).'] CLUBS</B>';

    if (!empty($arrClubs)) {
        foreach ($arrClubs as $intClubId => $arrClubData) {
            $strClubId = $arrClubData['clubNumber'];
            echo '<BR><BR>Club ID: ' . $arrClubData['id'] . ' - number: '. $strClubId;

            // Get fetch period for outgoing check-ins
            $arrFetchPeriod = $this->abcfinancial_syncs_model->getUpdatePeriodCheckins($intClubId, 'from_club');

            if (empty($arrFetchPeriod)) {
                continue;
            }

            // Create sync tracking record
            $intSyncId = $this->abcfinancial_syncs_model->createSyncCheckins($intClubId, 'from_club', $arrFetchPeriod);

            // Fetch member check-ins from API
            $arrRemoteCheckedInMembers = array();
            if(!$this->config->item('abcfinancial_api_testmode')) {
                $arrRemoteCheckedInMembers = $this->abcfinancial_API_model->getRemoteCheckedInMembers($strClubId, $arrFetchPeriod['period_start'], $arrFetchPeriod['period_end']);
            }

            // Process each member's check-ins
            $intTotal = count($arrRemoteCheckedInMembers);
            $intSuccess = 0;

            foreach ($arrRemoteCheckedInMembers as $arrOneRemoteCheckedInMember) {
                $strMemberId = $arrOneRemoteCheckedInMember['memberId'];
                $arrRemoteMemberCheckins = $arrOneRemoteCheckedInMember['checkins'];

                $blnInsertStatus = $this->abcfinancial_checkins_model->insertRawMemberCheckinsData($intSyncId, $intClubId, $strClubId, $strMemberId, $arrRemoteMemberCheckins);

                if ($blnInsertStatus) {
                    $intSuccess++;
                }
            }

            // Update sync status
            $strUpdateStatus = 'success';
            if ($intTotal != $intSuccess) {
                $strUpdateStatus = 'failed';
            }

            $this->abcfinancial_syncs_model->finishSyncCheckins($intSyncId, $intTotal, $strUpdateStatus);

            echo '<BR>Checkins: Successfully added ['.$intSuccess.'] members\' checkins out of ['.$intTotal.']';
        }
    }

    die;
}
```

#### 3. update_checkins_full()
**Purpose**: Processes raw check-in data into final database tables

```php
public function update_checkins_full() {
    $arrSyncsToProcess = $this->abcfinancial_syncs_model->findSyncsReadyToBeProcessedCheckins();

    if(empty($arrSyncsToProcess)) {
        return array();
    }

    $blnProcessingStatus = TRUE;

    foreach ($arrSyncsToProcess as $arrOneSyncToProcess) {
        echo '<BR><BR>Processing sync ID: ' . $arrOneSyncToProcess['id'];

        // Update sync status to processing
        $this->abcfinancial_syncs_model->finishSyncCheckins($arrOneSyncToProcess['id'], $arrOneSyncToProcess['fetched_items_count'], 'processing');

        // Process the raw data
        $blnProcessStatus = $this->abcfinancial_checkins_model->processFetchedCheckinsData($arrOneSyncToProcess);

        if ($blnProcessStatus) {
            // Mark as processed
            $this->abcfinancial_syncs_model->finishSyncCheckins($arrOneSyncToProcess['id'], $arrOneSyncToProcess['fetched_items_count'], 'processed');
            echo '<BR>Successfully processed sync ID: ' . $arrOneSyncToProcess['id'];
        } else {
            // Mark as failed
            $this->abcfinancial_syncs_model->finishSyncCheckins($arrOneSyncToProcess['id'], $arrOneSyncToProcess['fetched_items_count'], 'processing_failed');
            echo '<BR>Failed to process sync ID: ' . $arrOneSyncToProcess['id'];
            $blnProcessingStatus = FALSE;
        }
    }

    die('Finished processing checkins with status: '. ($blnProcessingStatus ? 'success' : 'failed'));
}
```

## API Models

### ABC Financial API Model
**File**: `application/models/services/Abcfinancial_API_model.php`

#### 1. getRemoteCheckinsComingToClub()
**Purpose**: Retrieves check-ins of members from other clubs checking into a specific club

```php
public function getRemoteCheckinsComingToClub(string $strClubNumber, string $strPeriodStart, string $strPeriodEnd):array {
    $this->load->model('services/abcfinancial_syncs_model');

    $strClubNumber = trim($strClubNumber);
    $strPeriodStart = trim($strPeriodStart);
    $strPeriodEnd = trim($strPeriodEnd);

    if (!strlen($strClubNumber) || !strlen(trim($strPeriodStart)) || !strlen(trim($strPeriodEnd))) {
        return array();
    }

    $arrClubData = $this->abcfinancial_clubs_model->getClubByRemoteId($strClubNumber);

    if (empty($arrClubData)) {
        return array();
    }

    $intClubId = $arrClubData['id'];

    if (!$intClubId) {
        return array();
    }

    $strPeriod = $strPeriodStart . ',' . $strPeriodEnd;
    $arrApiCallOptions = get_abcfinancial_api_call_options('checkins_list_incoming');
    $arrExtraParams = array(
        'checkInTimestampRange' => $strPeriod,
        'size' => $arrApiCallOptions['max_pagesize'],
        'page' => 1,
    );
    $arrExtraOptions = array();
    $arrRemoteCheckinsData = array();
    $blnGetNextPage = true;

    while ($blnGetNextPage) {
        $blnGetNextPage = false;

        $arrResponseBody = $this->getRemoteApiData($strClubNumber.$arrApiCallOptions['api_endpoint'], $arrExtraParams, $arrExtraOptions);
        $this->logApiResponseBody($arrResponseBody, 'checkins_coming_to_club', $strClubNumber, $arrExtraParams['page']);

        if ($arrResponseBody === false) {
            return array();
        }

        if (
            $arrResponseBody['status']['count'] == 0 ||
            !isset($arrResponseBody['checkins'])  || empty($arrResponseBody['checkins'])
        ) {
            return array();
        }

        if (!isset($arrResponseBody['checkins'][0]) || !isset($arrResponseBody['checkins'][0]['checkInId'])) {
            return array();
        }

        foreach ($arrResponseBody['checkins'] as $arrOneCheckin) {
            $arrRemoteCheckinsData[] = $arrOneCheckin;
        }

        // Check if there are more pages
        if ($arrResponseBody['status']['count'] == $arrApiCallOptions['max_pagesize']) {
            $arrExtraParams['page']++;
            $blnGetNextPage = true;
        }
    }

    return $arrRemoteCheckinsData;
}
```

#### 2. getRemoteCheckedInMembers()
**Purpose**: Retrieves check-ins of local club members at other clubs

```php
public function getRemoteCheckedInMembers(string $strClubNumber, string $strPeriodStart, string $strPeriodEnd):array {
    $this->load->model('services/abcfinancial_syncs_model');
    $strClubNumber = trim($strClubNumber);
    $strPeriodStart = trim($strPeriodStart);
    $strPeriodEnd = trim($strPeriodEnd);

    if (!strlen($strClubNumber) || !strlen(trim($strPeriodStart)) || !strlen(trim($strPeriodEnd))) {
        return array();
    }

    $arrClubData = $this->abcfinancial_clubs_model->getClubByRemoteId($strClubNumber);

    if (empty($arrClubData)) {
        return array();
    }

    $intClubId = $arrClubData['id'];

    if (!$intClubId) {
        return array();
    }

    $strPeriod = $strPeriodStart . ',' . $strPeriodEnd;
    $arrApiCallOptions = get_abcfinancial_api_call_options('checkins_list_outbounding');
    $arrExtraParams = array(
        'checkInTimestampRange' => $strPeriod,
        'size' => $arrApiCallOptions['max_pagesize'],
        'page' => 1,
    );
    $arrExtraOptions = array();
    $arrRemoteCheckinsData = array();
    $blnGetNextPage = true;

    while ($blnGetNextPage) {
        $blnGetNextPage = false;

        $arrResponseBody = $this->getRemoteApiData($strClubNumber.$arrApiCallOptions['api_endpoint'], $arrExtraParams, $arrExtraOptions);
        $this->logApiResponseBody($arrResponseBody, 'checkedin_members', $strClubNumber, $arrExtraParams['page']);

        if ($arrResponseBody === false) {
            return array();
        }

        if (
            $arrResponseBody['status']['count'] == 0 ||
            !isset($arrResponseBody['members']) || empty($arrResponseBody['members'])
        ) {
            return array();
        }

        foreach ($arrResponseBody['members'] as $arrOneMember) {
            $arrRemoteCheckinsData[] = $arrOneMember;
        }

        // Check if there are more pages
        if ($arrResponseBody['status']['count'] == $arrApiCallOptions['max_pagesize']) {
            $arrExtraParams['page']++;
            $blnGetNextPage = true;
        }
    }

    return $arrRemoteCheckinsData;
}
```

## Synchronization Models

### ABC Financial Syncs Model
**File**: `application/models/services/Abcfinancial_syncs_model.php`

#### 1. createSyncCheckins()
**Purpose**: Creates a new synchronization tracking record

```php
public function createSyncCheckins($intClubId, $strType, $arrFetchPeriod)
{
    $intClubId = intval($intClubId);
    $strType = strtolower(trim($strType));

    if (!$intClubId || !in_array($strType, array('coming_to_club', 'from_club'))) {
        return false;
    }

    $arrSyncData = array(
        'club_id' => $intClubId,
        'type' => $strType,
        'period_start' => $arrFetchPeriod['period_start'],
        'period_end' => $arrFetchPeriod['period_end'],
        'fetched_items_count' => 0,
        'status' => 'new',
        'finished_at' => 0,
        'created' => time(),
    );

    $this->db->insert('abc_sync_checkins', $arrSyncData);
    $intSyncId = $this->db->insert_id();

    return $intSyncId;
}
```

#### 2. getUpdatePeriodCheckins()
**Purpose**: Determines the time period for fetching check-in data

```php
public function getUpdatePeriodCheckins($intClubId, $strType)
{
    $this->load->model('public/datatypeformatter_model');

    switch ($strType) {
        case 'coming_to_club':
            $arrApiCallOptions = get_abcfinancial_api_call_options('checkins_list_incoming');
            break;

        case 'from_club':
        default:
            $arrApiCallOptions = get_abcfinancial_api_call_options('checkins_list_outbounding');
            break;
    }

    $arrFetchPeriod = array(
        'period_start' => date('Y-m-d H:i:s', time()),
        'period_end' => date('Y-m-d H:i:s', time() + $arrApiCallOptions['max_days'] * 86400),
    );

    // Get last successful fetch date
    $objQuery = $this->db->query('
        SELECT
            DATE_ADD(MAX(`period_end`), INTERVAL -1 HOUR) AS periodStart,
            LEAST(
                DATE_ADD(MAX(`period_end`), INTERVAL ' . $arrApiCallOptions['max_days'] . ' DAY),
                DATE_ADD( NOW(), INTERVAL ' . $this->config->item('abcfinancial_udt_timezone_difference') . ' HOUR)
            ) AS periodEnd
        FROM `abc_sync_checkins`
        WHERE
            `club_id` = ' . $intClubId . ' AND
            `type` = "' . $strType . '" AND
            `status` IN ("success", "processed")
    ');

    $arrResults = $objQuery->result_array();

    // If there is a result already then return it
    if (!empty($arrResults[0]['periodEnd'])) {
        return array('period_start' => $arrResults[0]['periodStart'], 'period_end' => $arrResults[0]['periodEnd']);
    }

    return $arrFetchPeriod;
}
```

#### 3. finishSyncCheckins()
**Purpose**: Updates synchronization record with completion status

```php
public function finishSyncCheckins($intSyncId, $intFetchedCount, $strStatus = 'success')
{
    $intSyncId = intval($intSyncId);
    $intFetchedCount = intval($intFetchedCount);
    $strStatus = strtolower(trim($strStatus));

    if (
        !$intSyncId ||
        !in_array($strStatus, array('new', 'processing', 'failed', 'success', 'processed', 'processing_failed'))
    ) {
        return false;
    }

    $this->db->reset_query();
    $query = $this->db->get_where('abc_sync_checkins', array('id' => $intSyncId), 1, 0);
    $arrSyncData = $query->result_array();

    if (empty($arrSyncData)) {
        return false;
    }

    $arrSyncData = $arrSyncData[0];
    $arrSyncData['fetched_items_count'] = $intFetchedCount;
    $arrSyncData['status'] = $strStatus;

    if (in_array($strStatus, array('failed', 'success', 'processed', 'processing_failed'))) {
        $arrSyncData['finished_at'] = time();
    }

    $this->db->reset_query();
    $this->db->where('id', $intSyncId);
    $this->db->set($arrSyncData);

    $this->db->update('abc_sync_checkins', $arrSyncData);

    return true;
}
```

## Check-ins Data Models

### ABC Financial Checkins Model
**File**: `application/models/services/Abcfinancial_checkins_model.php`

#### 1. createRawCheckin()
**Purpose**: Inserts raw check-in data into staging table

```php
public function createRawCheckin($intSyncId, $strSyncType, $intClubId, $strMemberId, $arrOneRemoteCheckin) {
    $intSyncId = intval($intSyncId);
    $intClubId = intval($intClubId);
    $strMemberId = trim($strMemberId);
    $strSyncType = trim($strSyncType);

    if(
        !$intSyncId ||
        !$intClubId ||
        !strlen($strMemberId) ||
        empty($arrOneRemoteCheckin) ||
        !in_array($strSyncType, array('coming_to_club', 'from_club'))
    ) {
        return false;
    }

    $arrCheckinData = array();
    $arrCheckinData['checkins_sync_id'] = $intSyncId;
    $arrCheckinData['sync_type'] = $strSyncType;
    $arrCheckinData['club_id_local'] = $intClubId;
    $arrCheckinData['memberIdRemote'] = $strMemberId;
    $arrCheckinData['checkInId'] = $arrOneRemoteCheckin['checkInId'];
    $arrCheckinData['checkinClubNumber'] = $arrOneRemoteCheckin['checkinClubNumber'];
    $arrCheckinData['homeClubNumber'] = $arrOneRemoteCheckin['homeClubNumber'];
    $arrCheckinData['checkInTimeStamp'] = $arrOneRemoteCheckin['checkInTimeStamp'];
    $arrCheckinData['updated'] = time();
    $arrCheckinData['created'] = time();

    $this->db->insert('abc_RAW_checkins', $arrCheckinData);
    $intRawCheckinId = $this->db->insert_id();

    return $intRawCheckinId;
}
```

#### 2. insertRawIncomingCheckinData()
**Purpose**: Processes incoming check-in data for storage

```php
public function insertRawIncomingCheckinData($intSyncId, $intClubId, $strClubNumber, $arrOneRemoteCheckin) {
    $intSyncId = intval($intSyncId);
    $intClubId = intval($intClubId);
    $strClubNumber = trim($strClubNumber);

    if(!$intSyncId || !$intClubId || !strlen($strClubNumber) || empty($arrOneRemoteCheckin)) {
        return false;
    }

    $strMemberId = $arrOneRemoteCheckin['memberId'];

    $arrCheckinData = array();
    $arrCheckinData['checkInId'] = $arrOneRemoteCheckin['checkInId'];
    $arrCheckinData['checkinClubNumber'] = $strClubNumber;
    $arrCheckinData['homeClubNumber'] = $arrOneRemoteCheckin['homeClubNumber'];
    $arrCheckinData['checkInTimeStamp'] = $arrOneRemoteCheckin['checkInTimeStamp'];

    $intRawCheckinId = $this->createRawCheckin($intSyncId, 'coming_to_club', $intClubId, $strMemberId, $arrCheckinData);

    return $intRawCheckinId;
}
```

#### 3. processFetchedCheckinsData()
**Purpose**: Processes raw check-in data into final database tables

```php
public function processFetchedCheckinsData(array $arrSyncData): bool
{
    if (empty($arrSyncData)) {
        return FALSE;
    }

    $intSyncId = (int) $arrSyncData['id'];
    $intClubId = (int) $arrSyncData['club_id'];
    $intFetchedDataCount = (int) $arrSyncData['fetched_items_count'];

    if (!$intSyncId || !$intClubId || !$intFetchedDataCount) {
        return FALSE;
    }

    $this->db->reset_query();

    $arrRawCheckins = $this->findRawCheckinsOfSync($intSyncId, $intClubId);

    if (empty($arrRawCheckins)) {
        return FALSE;
    }

    $blnProcessStatus = TRUE;

    do {
        $this->db->trans_start();

        foreach ($arrRawCheckins as $arrOneRawData) {
            $blnProcessStatus &= $this->updateCheckinDataFromRaw($arrOneRawData);
        }

        $this->db->trans_complete();
        $this->db->reset_query();

        $arrRawCheckins = $this->findRawCheckinsOfSync($intSyncId, $intClubId);
    } while (!empty($arrRawCheckins));

    return $blnProcessStatus;
}
```

#### 4. updateCheckinDataFromRaw()
**Purpose**: Transforms raw data into final check-in records

```php
private function updateCheckinDataFromRaw(array $arrOneRawData): bool
{
    if (empty($arrOneRawData) || !strlen(trim($arrOneRawData['checkInId'])) || !strlen(trim($arrOneRawData['memberIdRemote']))) {
        return TRUE; // Skip invalid data
    }

    // Transform/format RAW "string" data into proper datatype
    $arrCheckinDataProcessed = $this->processRawCheckinData($arrOneRawData);

    if (empty($arrCheckinDataProcessed)) {
        return FALSE;
    }

    // Check if check-in already exists
    $this->db->reset_query();
    $this->db->where('checkInIdRemote', $arrCheckinDataProcessed['checkInIdRemote']);
    $this->db->where('member_id_local', $arrCheckinDataProcessed['member_id_local']);
    $arrCheckinData = $this->db->get('abc_checkins', 1)->result_array();

    if (empty($arrCheckinData)) {
        // Insert new check-in record
        $this->db->insert('abc_checkins', $arrCheckinDataProcessed);
        $intCheckinDataId = $this->db->insert_id();
    } else {
        // Update existing check-in record
        $intCheckinDataId = (int) $arrCheckinData['id'];

        unset($arrCheckinDataProcessed['created']);

        $this->db->where('id', $intCheckinDataId);
        $this->db->set($arrCheckinDataProcessed);
        $this->db->update('abc_checkins', $arrCheckinDataProcessed);
    }

    return !empty($intCheckinDataId);
}
```

## Database Schema

### Core Tables

#### 1. abc_sync_checkins
**Purpose**: Tracks synchronization operations

```sql
CREATE TABLE abc_sync_checkins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    club_id INT NOT NULL,
    type ENUM('coming_to_club', 'from_club') NOT NULL,
    period_start DATETIME NOT NULL,
    period_end DATETIME NOT NULL,
    fetched_items_count INT DEFAULT 0,
    status ENUM('new', 'processing', 'success', 'failed', 'processed', 'processing_failed') DEFAULT 'new',
    finished_at INT DEFAULT 0,
    created INT NOT NULL,
    INDEX idx_club_type (club_id, type),
    INDEX idx_status (status),
    INDEX idx_period (period_start, period_end)
);
```

#### 2. abc_RAW_checkins
**Purpose**: Stores raw check-in data from API

```sql
CREATE TABLE abc_RAW_checkins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    checkins_sync_id INT NOT NULL,
    sync_type ENUM('coming_to_club', 'from_club') NOT NULL,
    club_id_local INT NOT NULL,
    memberIdRemote VARCHAR(50) NOT NULL,
    checkInId VARCHAR(100) NOT NULL,
    checkinClubNumber VARCHAR(20),
    homeClubNumber VARCHAR(20),
    checkInTimeStamp DATETIME,
    updated INT NOT NULL,
    created INT NOT NULL,
    INDEX idx_sync_id (checkins_sync_id),
    INDEX idx_club_member (club_id_local, memberIdRemote),
    INDEX idx_checkin_id (checkInId)
);
```

#### 3. abc_checkins
**Purpose**: Final processed check-in data

```sql
CREATE TABLE abc_checkins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    member_id_local INT,
    checkin_club_id_local INT,
    checkInIdRemote VARCHAR(100),
    checkinClubNumberRemote VARCHAR(20),
    homeClubNumberRemote VARCHAR(20),
    checkInTimeStamp DATETIME,
    raw_record_id INT,
    checkins_sync_id INT,
    updated INT NOT NULL,
    created INT NOT NULL,
    INDEX idx_member_checkin (member_id_local, checkInTimeStamp),
    INDEX idx_club_timestamp (checkin_club_id_local, checkInTimeStamp),
    INDEX idx_checkin_remote (checkInIdRemote),
    INDEX idx_raw_record (raw_record_id),
    FOREIGN KEY (member_id_local) REFERENCES abc_members(id),
    FOREIGN KEY (checkin_club_id_local) REFERENCES abc_clubs(id)
);
```

#### 4. abc_clubs
**Purpose**: Club information and mapping

```sql
CREATE TABLE abc_clubs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    clubNumber VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    club_name VARCHAR(100),
    active TINYINT(1) DEFAULT 1,
    created DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_club_number (clubNumber),
    INDEX idx_active (active)
);
```

## Cron Job Scheduling

### Master Orchestrator
**File**: `CRONs/batch_run_four.sh`

```bash
#!/bin/sh

# Lock file management
value=`cat /var/www/login.undefeatedtribe.com/CRONs/kpifour.run`
if [ "$value" = "1" ]
then
exit 0;
fi

echo 1 > /var/www/login.undefeatedtribe.com/CRONs/kpifour.run

cd "$(dirname "$0")"

# Check config file
file="./env_settings/env_settings"

if [ -f "$file" ]
then
    echo ""
    echo "FETCHING CHECK-INS"
    echo "~~~~~~~~~~~~~~~~~~"
    ./fetch_remote_checkins_cli.sh
    sleep 1

    echo ""
    echo "SYNCING CHECK-INS"
    echo "~~~~~~~~~~~~~~~~~"
    ./update_checkins_cli.sh

    echo ""
    echo "FETCHING EMPLOYEES"
    echo "~~~~~~~~~~~~~~~~~~"
    ./fetch_remote_employees_cli.sh
    sleep 1

    echo ""
    echo "SYNCING EMPLOYEES"
    echo "~~~~~~~~~~~~~~~~~"
    ./update_employees_cli.sh
fi

# Reset lock file
echo 0 > /var/www/login.undefeatedtribe.com/CRONs/kpifour.run
```

### Typical Cron Schedule
```bash
# Check-ins data synchronization - every 15 minutes
*/15 * * * * /var/www/login.undefeatedtribe.com/CRONs/batch_run_four.sh

# Alternative individual scheduling
# Fetch check-ins every 10 minutes
*/10 * * * * /var/www/login.undefeatedtribe.com/CRONs/fetch_remote_checkins_cli.sh

# Process check-ins every 15 minutes (offset by 5 minutes)
5,20,35,50 * * * * /var/www/login.undefeatedtribe.com/CRONs/update_checkins_cli.sh
```

## API Data Structure

### Incoming Check-ins API Response
```json
{
    "status": {
        "count": 25,
        "totalCount": 150
    },
    "checkins": [
        {
            "checkInId": "CHK123456789",
            "memberId": "MEM987654321",
            "homeClubNumber": "101",
            "checkInTimeStamp": "2024-01-15T14:30:00Z"
        }
    ]
}
```

### Outgoing Check-ins API Response
```json
{
    "status": {
        "count": 15,
        "totalCount": 75
    },
    "members": [
        {
            "memberId": "MEM987654321",
            "checkins": [
                {
                    "checkInId": "CHK123456789",
                    "clubNumber": "102",
                    "checkInTimeStamp": "2024-01-15T14:30:00Z"
                }
            ]
        }
    ]
}
```

## Data Flow Process

### 1. Fetch Phase
1. **Script Execution**: Cron triggers `fetch_remote_checkins_cli.sh`
2. **Environment Loading**: Script sources environment variables
3. **Service Call**: PHP executes `services/abcfinancial/fetch_all_checkins_coming_to_club`
4. **Club Iteration**: System processes each active club
5. **Period Calculation**: Determines fetch period based on last successful sync
6. **Sync Record Creation**: Creates tracking record in `abc_sync_checkins`
7. **API Calls**: Retrieves data from ABC Financial API with pagination
8. **Raw Data Storage**: Inserts data into `abc_RAW_checkins` table
9. **Sync Completion**: Updates sync record with status and count

### 2. Processing Phase
1. **Script Execution**: Cron triggers `update_checkins_cli.sh`
2. **Sync Discovery**: Finds sync records ready for processing
3. **Status Update**: Marks sync as 'processing'
4. **Raw Data Retrieval**: Fetches unprocessed raw records
5. **Data Transformation**: Converts raw data to proper format
6. **Member Matching**: Links check-ins to local member records
7. **Duplicate Handling**: Checks for existing check-in records
8. **Final Storage**: Inserts/updates records in `abc_checkins` table
9. **Completion**: Marks sync as 'processed'

## Error Handling and Monitoring

### Log Files Structure
```
logs/
├── fetch_checkins/
│   └── 2024-01-15/
│       ├── fetch_checkins_all_incoming_2024_01_15_14_30_15.log
│       └── fetch_checkins_members_to_foreign_2024_01_15_14_30_15.log
└── update_checkins/
    └── update_checkins_2024_01_15_14_45_20.log
```

### Common Error Scenarios
1. **API Timeout**: Network issues or ABC Financial API downtime
2. **Data Format Changes**: API response structure modifications
3. **Database Locks**: Concurrent processing conflicts
4. **Member Matching Failures**: Missing or invalid member IDs
5. **Sync Status Conflicts**: Multiple processes attempting same sync

### Monitoring Queries
```sql
-- Check recent sync status
SELECT club_id, type, status, fetched_items_count,
       FROM_UNIXTIME(created) as created_at,
       FROM_UNIXTIME(finished_at) as finished_at
FROM abc_sync_checkins
WHERE created > UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 24 HOUR))
ORDER BY created DESC;

-- Find failed syncs
SELECT * FROM abc_sync_checkins
WHERE status IN ('failed', 'processing_failed')
AND created > UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 7 DAY));

-- Check processing backlog
SELECT COUNT(*) as pending_syncs
FROM abc_sync_checkins
WHERE status = 'success';
```

## Implementation Guide

### Prerequisites
1. **CodeIgniter 3.x Framework**: PHP MVC framework
2. **MySQL 5.7+**: Database server with proper indexing
3. **PHP 7.4+**: With cURL and database extensions
4. **Cron Access**: Server-level cron job scheduling
5. **ABC Financial API Access**: Valid API credentials and endpoints

### Step-by-Step Implementation

#### 1. Database Setup
```sql
-- Create synchronization tracking table
CREATE TABLE abc_sync_checkins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    club_id INT NOT NULL,
    type ENUM('coming_to_club', 'from_club') NOT NULL,
    period_start DATETIME NOT NULL,
    period_end DATETIME NOT NULL,
    fetched_items_count INT DEFAULT 0,
    status ENUM('new', 'processing', 'success', 'failed', 'processed', 'processing_failed') DEFAULT 'new',
    finished_at INT DEFAULT 0,
    created INT NOT NULL,
    INDEX idx_club_type (club_id, type),
    INDEX idx_status (status),
    INDEX idx_period (period_start, period_end)
);

-- Create raw data staging table
CREATE TABLE abc_RAW_checkins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    checkins_sync_id INT NOT NULL,
    sync_type ENUM('coming_to_club', 'from_club') NOT NULL,
    club_id_local INT NOT NULL,
    memberIdRemote VARCHAR(50) NOT NULL,
    checkInId VARCHAR(100) NOT NULL,
    checkinClubNumber VARCHAR(20),
    homeClubNumber VARCHAR(20),
    checkInTimeStamp DATETIME,
    updated INT NOT NULL,
    created INT NOT NULL,
    INDEX idx_sync_id (checkins_sync_id),
    INDEX idx_club_member (club_id_local, memberIdRemote),
    INDEX idx_checkin_id (checkInId)
);

-- Create final check-ins table
CREATE TABLE abc_checkins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    member_id_local INT,
    checkin_club_id_local INT,
    checkInIdRemote VARCHAR(100),
    checkinClubNumberRemote VARCHAR(20),
    homeClubNumberRemote VARCHAR(20),
    checkInTimeStamp DATETIME,
    raw_record_id INT,
    checkins_sync_id INT,
    updated INT NOT NULL,
    created INT NOT NULL,
    INDEX idx_member_checkin (member_id_local, checkInTimeStamp),
    INDEX idx_club_timestamp (checkin_club_id_local, checkInTimeStamp),
    INDEX idx_checkin_remote (checkInIdRemote),
    INDEX idx_raw_record (raw_record_id)
);
```

#### 2. Model Implementation
1. **Create Abcfinancial_API_model.php**: API communication layer
2. **Create Abcfinancial_syncs_model.php**: Synchronization management
3. **Create Abcfinancial_checkins_model.php**: Data processing logic
4. **Create Abcfinancial_clubs_model.php**: Club management

#### 3. Controller Implementation
1. **Create services/Abcfinancial.php**: Main service controller
2. **Implement fetch methods**: `fetch_all_checkins_coming_to_club()` and `fetch_all_checkins_members_of_club_to_foreign()`
3. **Implement update method**: `update_checkins_full()`

#### 4. Configuration Setup
```php
// application/config/abcfinancial.php
$config['abcfinancial_api_testmode'] = FALSE;
$config['abcfinancial_udt_timezone_difference'] = -6; // CST offset
$config['abcfinancial_api_base_url'] = 'https://api.abcfinancial.com/';
$config['abcfinancial_api_timeout'] = 300;
$config['abcfinancial_max_retries'] = 3;

// API call options
function get_abcfinancial_api_call_options($type) {
    $options = [
        'checkins_list_incoming' => [
            'api_endpoint' => '/rest/checkins/incoming',
            'max_pagesize' => 1000,
            'max_days' => 7
        ],
        'checkins_list_outbounding' => [
            'api_endpoint' => '/rest/checkins/outbound',
            'max_pagesize' => 1000,
            'max_days' => 7
        ]
    ];

    return $options[$type] ?? [];
}
```

#### 5. Shell Scripts Setup
1. **Create environment settings**: `CRONs/env_settings/env_settings`
2. **Create fetch scripts**: `fetch_remote_checkins_cli.sh`
3. **Create update scripts**: `update_checkins_cli.sh`
4. **Set proper permissions**: `chmod +x *.sh`

#### 6. Cron Job Configuration
```bash
# Add to crontab
*/15 * * * * /var/www/html/CRONs/fetch_remote_checkins_cli.sh
*/15 * * * * /var/www/html/CRONs/update_checkins_cli.sh

# Or use master orchestrator
*/15 * * * * /var/www/html/CRONs/batch_run_four.sh
```

#### 7. Log Directory Setup
```bash
mkdir -p logs/fetch_checkins
mkdir -p logs/update_checkins
chmod 755 logs/
chmod 755 logs/fetch_checkins/
chmod 755 logs/update_checkins/
```

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. API Connection Failures
**Symptoms**: Empty log files, connection timeout errors
**Solutions**:
- Verify API credentials and endpoints
- Check network connectivity to ABC Financial servers
- Increase timeout values in configuration
- Verify SSL certificate handling

#### 2. Database Lock Issues
**Symptoms**: Processing hangs, duplicate key errors
**Solutions**:
- Implement proper transaction handling
- Add database connection pooling
- Optimize table indexes
- Monitor concurrent process execution

#### 3. Memory Exhaustion
**Symptoms**: PHP fatal errors, incomplete processing
**Solutions**:
- Increase PHP memory limit
- Implement batch processing for large datasets
- Add garbage collection calls
- Process data in smaller chunks

#### 4. Sync Status Inconsistencies
**Symptoms**: Records stuck in 'processing' status
**Solutions**:
- Implement cleanup procedures for stale syncs
- Add timeout handling for long-running processes
- Monitor and reset stuck synchronizations
- Implement proper error recovery

#### 5. Data Quality Issues
**Symptoms**: Missing check-ins, incorrect timestamps
**Solutions**:
- Validate API response data structure
- Implement data quality checks
- Add logging for data transformation steps
- Monitor member ID matching accuracy

### Performance Optimization

#### 1. Database Optimization
```sql
-- Add composite indexes for common queries
CREATE INDEX idx_checkins_member_timestamp ON abc_checkins (member_id_local, checkInTimeStamp DESC);
CREATE INDEX idx_checkins_club_timestamp ON abc_checkins (checkin_club_id_local, checkInTimeStamp DESC);

-- Partition large tables by date
ALTER TABLE abc_checkins PARTITION BY RANGE (YEAR(checkInTimeStamp)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026)
);
```

#### 2. API Call Optimization
- Implement intelligent pagination
- Use appropriate page sizes based on data volume
- Add retry logic with exponential backoff
- Cache API responses when appropriate

#### 3. Processing Optimization
- Process data in batches to avoid memory issues
- Use database transactions for data consistency
- Implement parallel processing for multiple clubs
- Add progress tracking and resumption capabilities

This comprehensive documentation provides everything needed to understand, implement, and maintain the Check-ins Data Synchronization System. The system ensures reliable, automated synchronization of member check-in data between ABC Financial's API and the local UndefeatedTribe database.
